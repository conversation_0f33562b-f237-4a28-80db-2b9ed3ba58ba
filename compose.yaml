version: '3.8'

services:
  db:
    image: postgres:12
    restart: always
    volumes:
      - app-db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  adminer:
    image: adminer:latest
    restart: always
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DESIGN=pepa-linha-dark
    depends_on:
      db:
        condition: service_healthy

  pgadmin:
    image: dpage/pgadmin4
    restart: always
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL?Variable not set}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD?Variable not set}
    depends_on:
      db:
        condition: service_healthy

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    restart: always
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:              # <── ensure Redis is up first
        condition: service_started
    env_file:
      - .env
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=${POSTGRES_USER?Variable not set}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD?Variable not set}
      - POSTGRES_DB=${POSTGRES_DB?Variable not set}
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis   # <── tell backend to use the service name
      - REDIS_PORT=6379
      - ENVIRONMENT=local
    command: ["/app/.venv/bin/fastapi", "dev", "src/main.py", "--host", "0.0.0.0", "--port", "8000"]
    develop:
      watch:
        - action: rebuild
          path: /backend
        - action: sync
          path: ./frontend
          target: /frontend
    volumes:
      - ./backend:/app
      - ./frontend:/frontend
      - /app/.venv
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  app-db-data:
  redis-data:

networks:
  default:
    driver: bridge
