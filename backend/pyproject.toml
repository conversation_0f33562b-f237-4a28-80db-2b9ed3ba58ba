[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiosqlite>=0.21.0",
    "apscheduler>=3.11.0",
    "asyncpg>=0.30.0",
    "databases>=0.9.0",
    "fastapi[standard]>=0.116.1",
    "limits[redis]>=5.4.0",
    "loguru>=0.7.3",
    "passlib>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pydantic-ai>=0.4.2",
    "pydantic-settings>=2.10.1",
    "pyjwt>=2.10.1",
    "redis>=5.2.1",
    "slowapi>=0.1.9",
    "sqlalchemy>=2.0.41",
    "sse-starlette>=2.4.1",
    "fasthx[jinja]>=0.2024.1"
]
