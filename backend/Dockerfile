FROM python:3.13-slim
WORKDIR /app

# Copy the .env file from the root directory
COPY .env .

ENV UV_HTTP_TIMEOUT=400
# Copy backend directory contents
COPY backend/ .
# Copy frontend directory
COPY frontend/ /frontend/
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
ENV PATH="/app/.venv/bin:$PATH"

# Install essential system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    fastapi[standard] \
    uvicorn \
    jinja2 \
    aiofiles \
    python-multipart \
    && pip install --no-cache-dir fasthx || echo "FastHX installation failed, continuing without it"

CMD ["/usr/local/bin/uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
