# src/tasks/models.py
import uuid
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    String,
    Table,
    Text,
    Uuid,
    func,
)

from src.database import metadata
from src.utils import TaskPriority

task_table = Table(
    "tasks",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    Column("name", String(100), nullable=False),
    Column("description", Text, nullable=False),
    <PERSON>umn("due_date", DateTime(timezone=True), nullable=True),
    <PERSON>umn("priority", Enum(TaskPriority), nullable=False, default=TaskPriority.LOW),
    <PERSON>umn("is_done", <PERSON><PERSON><PERSON>, nullable=False),
    <PERSON>umn("created_at", Date, default=func.now(), nullable=False, index=True),
    <PERSON>umn("user_id", Uuid, ForeignKey("users.id"), nullable=False),
)

Index("tasks_created_at_idx", task_table.c.created_at)
Index("tasks_user_id_idx", task_table.c.user_id)
Index("taskidx", task_table.c.id)
