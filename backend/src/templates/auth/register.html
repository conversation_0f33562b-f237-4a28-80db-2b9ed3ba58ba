{% extends "base.html" %}

{% block title %}Register - TomoPlan{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Or
                <a href="/login" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                    sign in to your existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" x-data="registerForm()" @submit.prevent="submitRegister()">
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="firstName" class="form-label">First Name</label>
                        <input 
                            id="firstName" 
                            name="firstName" 
                            type="text" 
                            required 
                            x-model="firstName"
                            :class="{ 'form-input error': errors.firstName, 'form-input': !errors.firstName }"
                            placeholder="First name"
                        >
                        <p x-show="errors.firstName" x-text="errors.firstName" class="form-error"></p>
                    </div>
                    <div>
                        <label for="lastName" class="form-label">Last Name</label>
                        <input 
                            id="lastName" 
                            name="lastName" 
                            type="text" 
                            required 
                            x-model="lastName"
                            :class="{ 'form-input error': errors.lastName, 'form-input': !errors.lastName }"
                            placeholder="Last name"
                        >
                        <p x-show="errors.lastName" x-text="errors.lastName" class="form-error"></p>
                    </div>
                </div>

                <div>
                    <label for="email" class="form-label">Email Address</label>
                    <input 
                        id="email" 
                        name="email" 
                        type="email" 
                        autocomplete="email" 
                        required 
                        x-model="email"
                        @blur="validateEmail()"
                        :class="{ 'form-input error': errors.email, 'form-input': !errors.email }"
                        placeholder="Email address"
                    >
                    <p x-show="errors.email" x-text="errors.email" class="form-error"></p>
                </div>

                <div>
                    <label for="password" class="form-label">Password</label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        autocomplete="new-password" 
                        required 
                        x-model="password"
                        @blur="validatePassword()"
                        :class="{ 'form-input error': errors.password, 'form-input': !errors.password }"
                        placeholder="Password (min. 6 characters)"
                    >
                    <p x-show="errors.password" x-text="errors.password" class="form-error"></p>
                </div>

                <div>
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <input 
                        id="confirmPassword" 
                        name="confirmPassword" 
                        type="password" 
                        autocomplete="new-password" 
                        required 
                        x-model="confirmPassword"
                        @blur="validateConfirmPassword()"
                        :class="{ 'form-input error': errors.confirmPassword, 'form-input': !errors.confirmPassword }"
                        placeholder="Confirm password"
                    >
                    <p x-show="errors.confirmPassword" x-text="errors.confirmPassword" class="form-error"></p>
                </div>
            </div>

            <div x-show="errors.general" class="rounded-md bg-red-50 dark:bg-red-900 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Registration Error
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <p x-text="errors.general"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center">
                <input 
                    id="terms" 
                    name="terms" 
                    type="checkbox" 
                    required
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
                >
                <label for="terms" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                    I agree to the 
                    <a href="#" class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">Terms of Service</a> 
                    and 
                    <a href="#" class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">Privacy Policy</a>
                </label>
            </div>

            <div>
                <button 
                    type="submit" 
                    :disabled="loading"
                    :class="{ 'opacity-50 cursor-not-allowed': loading }"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg x-show="!loading" class="h-5 w-5 text-blue-500 group-hover:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                        <svg x-show="loading" class="animate-spin h-5 w-5 text-blue-500" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    <span x-text="loading ? 'Creating account...' : 'Create account'"></span>
                </button>
            </div>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300 dark:border-gray-600" />
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                            Already have an account?
                        </span>
                    </div>
                </div>

                <div class="mt-6">
                    <a 
                        href="/login"
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Sign in instead
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Redirect if already authenticated
    document.addEventListener('DOMContentLoaded', function() {
        const token = localStorage.getItem('token');
        if (token) {
            window.location.href = '/dashboard';
        }
    });
</script>
{% endblock %}
