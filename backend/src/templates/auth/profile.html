{% extends "base.html" %}

{% block title %}Profile - TomoPlan{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8" x-data="profileForm()">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
                Profile Settings
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Manage your account information and preferences
            </p>
        </div>
    </div>

    <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Profile Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Profile Information
                </h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Update your account's profile information and email address.
                </p>
            </div>
            <div class="card-body">
                <form @submit.prevent="updateProfile()" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="form-label">First Name</label>
                            <input 
                                type="text" 
                                x-model="firstName"
                                required
                                class="form-input"
                                :class="{ 'error': errors.firstName }"
                                placeholder="First name"
                            >
                            <p x-show="errors.firstName" x-text="errors.firstName" class="form-error"></p>
                        </div>
                        <div>
                            <label class="form-label">Last Name</label>
                            <input 
                                type="text" 
                                x-model="lastName"
                                required
                                class="form-input"
                                :class="{ 'error': errors.lastName }"
                                placeholder="Last name"
                            >
                            <p x-show="errors.lastName" x-text="errors.lastName" class="form-error"></p>
                        </div>
                    </div>

                    <div>
                        <label class="form-label">Email Address</label>
                        <input 
                            type="email" 
                            x-model="email"
                            required
                            class="form-input"
                            :class="{ 'error': errors.email }"
                            placeholder="Email address"
                        >
                        <p x-show="errors.email" x-text="errors.email" class="form-error"></p>
                    </div>

                    <div x-show="errors.general" class="rounded-md bg-red-50 dark:bg-red-900 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Update Error
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <p x-text="errors.general"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button 
                            type="submit" 
                            :disabled="loading"
                            class="btn btn-primary"
                            :class="{ 'opacity-50 cursor-not-allowed': loading }"
                        >
                            <span x-text="loading ? 'Updating...' : 'Update Profile'"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Change Password -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Change Password
                </h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Ensure your account is using a long, random password to stay secure.
                </p>
            </div>
            <div class="card-body">
                <form @submit.prevent="updatePassword()" class="space-y-4">
                    <div>
                        <label class="form-label">Current Password</label>
                        <input 
                            type="password" 
                            x-model="currentPassword"
                            required
                            class="form-input"
                            :class="{ 'error': passwordErrors.currentPassword }"
                            placeholder="Current password"
                        >
                        <p x-show="passwordErrors.currentPassword" x-text="passwordErrors.currentPassword" class="form-error"></p>
                    </div>

                    <div>
                        <label class="form-label">New Password</label>
                        <input 
                            type="password" 
                            x-model="newPassword"
                            required
                            minlength="6"
                            class="form-input"
                            :class="{ 'error': passwordErrors.newPassword }"
                            placeholder="New password (min. 6 characters)"
                        >
                        <p x-show="passwordErrors.newPassword" x-text="passwordErrors.newPassword" class="form-error"></p>
                    </div>

                    <div>
                        <label class="form-label">Confirm New Password</label>
                        <input 
                            type="password" 
                            x-model="confirmNewPassword"
                            required
                            class="form-input"
                            :class="{ 'error': passwordErrors.confirmNewPassword }"
                            placeholder="Confirm new password"
                        >
                        <p x-show="passwordErrors.confirmNewPassword" x-text="passwordErrors.confirmNewPassword" class="form-error"></p>
                    </div>

                    <div x-show="passwordErrors.general" class="rounded-md bg-red-50 dark:bg-red-900 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Password Update Error
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <p x-text="passwordErrors.general"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button 
                            type="submit" 
                            :disabled="passwordLoading"
                            class="btn btn-primary"
                            :class="{ 'opacity-50 cursor-not-allowed': passwordLoading }"
                        >
                            <span x-text="passwordLoading ? 'Updating...' : 'Update Password'"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Account Statistics -->
    <div class="mt-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Account Statistics
                </h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                    <div class="bg-gray-50 dark:bg-gray-700 overflow-hidden rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Total Tasks
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="stats.totalTasks || 0">
                                            0
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 overflow-hidden rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Completed
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="stats.completedTasks || 0">
                                            0
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 overflow-hidden rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Completion Rate
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="stats.completionRate || '0%'">
                                            0%
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 overflow-hidden rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Days Active
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="stats.daysActive || 0">
                                            0
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mt-6">
        <div class="card border-red-200 dark:border-red-700">
            <div class="card-header bg-red-50 dark:bg-red-900">
                <h3 class="text-lg font-medium text-red-800 dark:text-red-200">
                    Danger Zone
                </h3>
                <p class="mt-1 text-sm text-red-600 dark:text-red-300">
                    Irreversible and destructive actions.
                </p>
            </div>
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Delete Account</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            Permanently delete your account and all associated data.
                        </p>
                    </div>
                    <button 
                        @click="confirmDeleteAccount()"
                        class="btn btn-danger"
                    >
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Load user stats on page load
    document.addEventListener('DOMContentLoaded', async function() {
        // This would typically load user statistics from the API
        // For now, we'll simulate with localStorage or API calls
    });
</script>
{% endblock %}
