<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TomoPlan - AI-Powered Task Management Platform</title>
    <meta name="description" content="Transform your productivity with TomoPlan's AI-powered task management. Smart planning, real-time collaboration, and intelligent insights.">

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        accent: {
                            500: '#8b5cf6',
                            600: '#7c3aed',
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'fade-in-up': 'fadeInUp 0.8s ease-out',
                        'fade-in-down': 'fadeInDown 0.8s ease-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        }
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300" x-data="landingPage()">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">TomoPlan</h1>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        Features
                    </a>
                    <a href="#how-it-works" class="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        How It Works
                    </a>
                    <a href="#pricing" class="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        Pricing
                    </a>
                    <button @click="darkMode = !darkMode" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/login" class="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        Sign In
                    </a>
                    <a href="/register" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                        Get Started Free
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-purple-900 overflow-hidden pt-16">
        <!-- Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-float"></div>
            <div class="absolute top-40 -left-32 w-64 h-64 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-20 animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-40 right-20 w-48 h-48 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-20 animate-float" style="animation-delay: 4s;"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-12 lg:gap-8 items-center min-h-screen py-20">
                <div class="sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left">
                    <!-- Badge -->
                    <div class="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6 animate-fade-in-down">
                        <i class="fas fa-sparkles mr-2"></i>
                        AI-Powered Productivity
                    </div>

                    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white sm:text-5xl md:text-6xl lg:text-5xl xl:text-6xl animate-fade-in-up">
                        <span class="block">Transform Your</span>
                        <span class="block bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">Tomorrow, Today</span>
                    </h1>

                    <p class="mt-6 text-lg text-gray-600 dark:text-gray-300 sm:text-xl max-w-3xl animate-fade-in-up" style="animation-delay: 0.2s;">
                        Stop overthinking your daily tasks. Just tell TomoPlan what you want to accomplish tomorrow, and our AI will break it down into clear, actionable steps with time estimates. Focus on execution, not planning.
                    </p>

                    <!-- Key Benefits -->
                    <div class="mt-8 space-y-3 animate-fade-in-up" style="animation-delay: 0.4s;">
                        <div class="flex items-center text-gray-600 dark:text-gray-300">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>3+ tasks → Smart AI breakdown with time estimates</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-300">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Real-time notifications and progress tracking</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-300">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Analytics to optimize your productivity</span>
                        </div>
                    </div>

                    <div class="mt-10 sm:flex sm:justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-4 animate-fade-in-up" style="animation-delay: 0.6s;">
                        <a href="/register" class="w-full sm:w-auto flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-rocket mr-2"></i>
                            Start Planning Free
                        </a>
                        <a href="#demo" class="w-full sm:w-auto flex items-center justify-center px-8 py-4 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-xl text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                            <i class="fas fa-play mr-2"></i>
                            Watch Demo
                        </a>
                    </div>

                    <!-- Social Proof -->
                    <div class="mt-12 animate-fade-in-up" style="animation-delay: 0.8s;">
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Trusted by productivity enthusiasts</p>
                        <div class="flex items-center space-x-6">
                            <div class="flex items-center">
                                <div class="flex -space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full border-2 border-white dark:border-gray-800"></div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full border-2 border-white dark:border-gray-800"></div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-2 border-white dark:border-gray-800"></div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full border-2 border-white dark:border-gray-800"></div>
                                </div>
                                <span class="ml-3 text-sm text-gray-600 dark:text-gray-300">1000+ users</span>
                            </div>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">4.9/5 rating</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hero Image/Animation -->
                <div class="mt-16 lg:mt-0 lg:col-span-6">
                    <div class="relative mx-auto w-full max-w-lg animate-fade-in-up" style="animation-delay: 1s;">
                        <!-- Main Dashboard Preview -->
                        <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden transform rotate-3 hover:rotate-0 transition-transform duration-500">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-8 flex items-center px-4">
                                <div class="flex space-x-2">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Tomorrow's Plan</h3>
                                        <span class="text-sm text-green-600 dark:text-green-400">AI Generated</span>
                                    </div>
                                    <div class="space-y-3">
                                        <div class="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">Review project proposal</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">45 minutes • High Priority</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                            <div class="w-4 h-4 bg-purple-500 rounded mr-3"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">Team standup meeting</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">30 minutes • Medium Priority</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                            <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">Update documentation</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">60 minutes • Low Priority</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-4 -right-4 bg-gradient-to-r from-green-400 to-green-600 text-white p-3 rounded-xl shadow-lg animate-pulse-slow">
                            <i class="fas fa-check text-sm"></i>
                        </div>
                        <div class="absolute -bottom-4 -left-4 bg-gradient-to-r from-blue-400 to-blue-600 text-white p-3 rounded-xl shadow-lg animate-pulse-slow" style="animation-delay: 1s;">
                            <i class="fas fa-brain text-sm"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-20 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-base text-blue-600 dark:text-blue-400 font-semibold tracking-wide uppercase">How It Works</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                    Three Simple Steps to Better Productivity
                </p>
                <p class="mt-4 max-w-2xl text-xl text-gray-600 dark:text-gray-300 mx-auto">
                    From chaos to clarity in minutes, not hours
                </p>
            </div>

            <div class="relative">
                <!-- Connection Line -->
                <div class="hidden lg:block absolute top-1/2 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-blue-200 via-purple-200 to-blue-200 dark:from-blue-800 dark:via-purple-800 dark:to-blue-800"></div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
                    <!-- Step 1 -->
                    <div class="relative text-center group">
                        <div class="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:shadow-xl transition-shadow duration-300 relative z-10">
                            1
                        </div>
                        <h3 class="mt-6 text-xl font-semibold text-gray-900 dark:text-white">Tell Us Your Tasks</h3>
                        <p class="mt-4 text-gray-600 dark:text-gray-300">
                            Simply list 3 or more things you want to accomplish tomorrow. No need to overthink it.
                        </p>
                        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="text-sm text-gray-600 dark:text-gray-300 space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-edit text-blue-500 mr-2"></i>
                                    <span>"Finish project proposal"</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-edit text-blue-500 mr-2"></i>
                                    <span>"Team meeting prep"</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-edit text-blue-500 mr-2"></i>
                                    <span>"Update documentation"</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="relative text-center group">
                        <div class="mx-auto w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:shadow-xl transition-shadow duration-300 relative z-10">
                            2
                        </div>
                        <h3 class="mt-6 text-xl font-semibold text-gray-900 dark:text-white">AI Creates Your Plan</h3>
                        <p class="mt-4 text-gray-600 dark:text-gray-300">
                            Our AI breaks down each task into actionable steps with time estimates and priority levels.
                        </p>
                        <div class="mt-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <div class="flex items-center justify-center space-x-2 text-purple-600 dark:text-purple-400">
                                <i class="fas fa-brain animate-pulse"></i>
                                <span class="text-sm font-medium">AI Processing...</span>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                                    <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="relative text-center group">
                        <div class="mx-auto w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:shadow-xl transition-shadow duration-300 relative z-10">
                            3
                        </div>
                        <h3 class="mt-6 text-xl font-semibold text-gray-900 dark:text-white">Execute & Track</h3>
                        <p class="mt-4 text-gray-600 dark:text-gray-300">
                            Follow your optimized plan with real-time progress tracking and smart notifications.
                        </p>
                        <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="space-y-2">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-300">Daily Progress</span>
                                    <span class="text-green-600 dark:text-green-400 font-medium">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-base text-blue-600 dark:text-blue-400 font-semibold tracking-wide uppercase">Features</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                    Everything you need to stay productive
                </p>
                <p class="mt-4 max-w-2xl text-xl text-gray-600 dark:text-gray-300 mx-auto">
                    Powerful features designed to transform how you plan and execute your daily tasks
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-brain text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">AI-Powered Planning</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Advanced AI analyzes your tasks and creates optimized daily plans with time estimates and priority levels.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Smart task breakdown</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Time estimation</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Priority optimization</li>
                        </ul>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-bell text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Real-time Notifications</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Stay on track with intelligent notifications that remind you of deadlines and celebrate your progress.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Smart reminders</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Progress celebrations</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Deadline alerts</li>
                        </ul>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Analytics & Insights</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Track your productivity patterns and get insights to continuously improve your workflow.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Productivity metrics</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Completion trends</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Time analysis</li>
                        </ul>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-mobile-alt text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Mobile Responsive</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Access your tasks anywhere with our fully responsive design that works perfectly on all devices.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mobile optimized</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Offline support</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Cross-platform</li>
                        </ul>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-shield-alt text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Secure & Private</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Your data is protected with enterprise-grade security and privacy measures.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>End-to-end encryption</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>GDPR compliant</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Data ownership</li>
                        </ul>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="relative group">
                    <div class="absolute inset-0 bg-gradient-to-r from-pink-500 to-red-600 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <div class="relative p-8 bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center text-white mb-6">
                            <i class="fas fa-users text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Team Collaboration</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Share tasks and collaborate with your team members for better coordination.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Task sharing</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Team dashboards</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Progress tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-base text-blue-600 dark:text-blue-400 font-semibold tracking-wide uppercase">Pricing</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                    Simple, transparent pricing
                </p>
                <p class="mt-4 max-w-2xl text-xl text-gray-600 dark:text-gray-300 mx-auto">
                    Start free, upgrade when you need more power
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- Free Plan -->
                <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-semibold text-gray-900 dark:text-white">Free</h3>
                        <p class="mt-2 text-gray-600 dark:text-gray-300">Perfect for getting started</p>
                        <div class="mt-6">
                            <span class="text-4xl font-extrabold text-gray-900 dark:text-white">$0</span>
                            <span class="text-gray-600 dark:text-gray-300">/month</span>
                        </div>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Up to 10 tasks per day</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Basic AI planning</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Mobile app access</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Basic analytics</span>
                        </li>
                    </ul>
                    <div class="mt-8">
                        <a href="/register" class="w-full flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            Get Started Free
                        </a>
                    </div>
                </div>

                <!-- Pro Plan -->
                <div class="relative bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 transform scale-105">
                    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900 px-4 py-1 rounded-full text-sm font-medium">
                            Most Popular
                        </span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-semibold text-white">Pro</h3>
                        <p class="mt-2 text-blue-100">For power users</p>
                        <div class="mt-6">
                            <span class="text-4xl font-extrabold text-white">$9</span>
                            <span class="text-blue-100">/month</span>
                        </div>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Unlimited tasks</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Advanced AI planning</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Priority support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Advanced analytics</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Team collaboration</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-300 mr-3"></i>
                            <span class="text-white">Custom integrations</span>
                        </li>
                    </ul>
                    <div class="mt-8">
                        <a href="/register" class="w-full flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors">
                            Start Pro Trial
                        </a>
                    </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-semibold text-gray-900 dark:text-white">Enterprise</h3>
                        <p class="mt-2 text-gray-600 dark:text-gray-300">For large teams</p>
                        <div class="mt-6">
                            <span class="text-4xl font-extrabold text-gray-900 dark:text-white">$29</span>
                            <span class="text-gray-600 dark:text-gray-300">/month</span>
                        </div>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Everything in Pro</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Unlimited team members</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Advanced security</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Custom AI models</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">Dedicated support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-600 dark:text-gray-300">SLA guarantee</span>
                        </li>
                    </ul>
                    <div class="mt-8">
                        <a href="/register" class="w-full flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="mt-20">
                <h3 class="text-2xl font-bold text-center text-gray-900 dark:text-white mb-12">Frequently Asked Questions</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Is there a free trial?</h4>
                        <p class="text-gray-600 dark:text-gray-300">Yes! Our free plan gives you access to core features forever. Pro and Enterprise plans come with a 14-day free trial.</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Can I cancel anytime?</h4>
                        <p class="text-gray-600 dark:text-gray-300">Absolutely. You can cancel your subscription at any time with no questions asked. Your data remains accessible.</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">How does the AI work?</h4>
                        <p class="text-gray-600 dark:text-gray-300">Our AI analyzes your tasks, considers your work patterns, and creates optimized plans with time estimates and priority levels.</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Is my data secure?</h4>
                        <p class="text-gray-600 dark:text-gray-300">Yes. We use enterprise-grade encryption and are GDPR compliant. Your data is never shared with third parties.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="relative py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="absolute top-0 left-0 w-full h-full">
            <div class="absolute top-20 left-20 w-32 h-32 bg-white opacity-10 rounded-full animate-float"></div>
            <div class="absolute bottom-20 right-20 w-24 h-24 bg-white opacity-10 rounded-full animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white opacity-5 rounded-full animate-pulse-slow"></div>
        </div>

        <div class="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl font-extrabold text-white sm:text-5xl">
                <span class="block">Ready to transform</span>
                <span class="block">your productivity?</span>
            </h2>
            <p class="mt-6 text-xl text-blue-100 max-w-2xl mx-auto">
                Join thousands of users who have revolutionized their daily workflow with AI-powered task management. Start your journey to better productivity today.
            </p>

            <!-- Stats -->
            <div class="mt-12 grid grid-cols-1 sm:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">10,000+</div>
                    <div class="text-blue-200">Tasks Planned</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">1,000+</div>
                    <div class="text-blue-200">Happy Users</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">95%</div>
                    <div class="text-blue-200">Satisfaction Rate</div>
                </div>
            </div>

            <div class="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/register" class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-blue-600 bg-white hover:bg-gray-50 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <i class="fas fa-rocket mr-2"></i>
                    Start Free Today
                </a>
                <a href="#demo" class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl text-white border-2 border-white hover:bg-white hover:text-blue-600 transition-all duration-200">
                    <i class="fas fa-play mr-2"></i>
                    Watch Demo
                </a>
            </div>

            <p class="mt-6 text-sm text-blue-200">
                No credit card required • Free forever plan available • Cancel anytime
            </p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 dark:bg-black">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <h3 class="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TomoPlan</h3>
                    </div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Transform your productivity with AI-powered task management. Stop overthinking, start executing.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-discord text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Product -->
                <div>
                    <h4 class="text-white font-semibold mb-4">Product</h4>
                    <ul class="space-y-3">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Company -->
                <div>
                    <h4 class="text-white font-semibold mb-4">Company</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Support</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; 2024 TomoPlan. All rights reserved.
                </p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Alpine.js data function
        function landingPage() {
            return {
                darkMode: localStorage.getItem('darkMode') === 'true' || false,

                init() {
                    // Set initial dark mode
                    if (this.darkMode) {
                        document.documentElement.classList.add('dark');
                    }

                    // Watch for dark mode changes
                    this.$watch('darkMode', (value) => {
                        localStorage.setItem('darkMode', value);
                        if (value) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    });

                    // Redirect if already authenticated
                    const token = localStorage.getItem('token');
                    if (token) {
                        window.location.href = '/dashboard';
                    }

                    // Smooth scrolling for anchor links
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const target = document.querySelector(this.getAttribute('href'));
                            if (target) {
                                target.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        });
                    });
                }
            }
        }
    </script>
</body>
</html>
