<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TomoPlan - AI-Powered Task Management{% endblock %}</title>

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom animations and styles -->
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes fade-in-down {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes pulse-slow {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        .animate-fade-in-up {
            animation: fade-in-up 0.6s ease-out;
        }
        .animate-fade-in-down {
            animation: fade-in-down 0.6s ease-out;
        }
        .animate-pulse-slow {
            animation: pulse-slow 2s ease-in-out infinite;
        }
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        .htmx-request.htmx-indicator {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200"
      x-data="app()"
      x-init="init()">

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-4 right-4 z-50">
        <div class="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-40 space-y-2" x-data="notifications()">
        <template x-for="notification in notifications" :key="notification.id">
            <div class="bg-white dark:bg-gray-800 border-l-4 border-blue-500 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300"
                 x-show="notification.show"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform translate-x-full"
                 x-transition:enter-end="opacity-100 transform translate-x-0"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform translate-x-0"
                 x-transition:leave-end="opacity-0 transform translate-x-full">
                <div class="flex">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="notification.title"></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300" x-text="notification.message"></p>
                    </div>
                    <button @click="removeNotification(notification.id)" class="ml-4 text-gray-400 hover:text-gray-600">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </template>
    </div>

    {% if request.url.path not in ['/login', '/register'] %}
    <!-- Navigation Header -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-blue-600 dark:text-blue-400">TomoPlan</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="/dashboard" class="nav-link" :class="{ 'border-blue-500 text-blue-600': isCurrentPage('/dashboard') }">Dashboard</a>
                        <a href="/tasks" class="nav-link" :class="{ 'border-blue-500 text-blue-600': isCurrentPage('/tasks') }">Tasks</a>
                        <a href="/planning" class="nav-link" :class="{ 'border-blue-500 text-blue-600': isCurrentPage('/planning') }">AI Planning</a>
                        <a href="/analytics" class="nav-link" :class="{ 'border-blue-500 text-blue-600': isCurrentPage('/analytics') }">Analytics</a>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg x-show="!darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <svg x-show="darkMode" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </button>

                    <!-- Notifications -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span x-show="unreadCount > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" x-text="unreadCount"></span>
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=User&background=3b82f6&color=fff" alt="User avatar">
                        </button>
                        <div x-show="open" @click.away="open = false" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
                                <button @click="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Sign out</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <main class="{% if request.url.path not in ['/login', '/register'] %}max-w-7xl mx-auto py-6 sm:px-6 lg:px-8{% endif %}">
        {% block content %}{% endblock %}
    </main>

    <!-- Core Alpine.js Application -->
    <script>
        // Main Alpine.js application
        function app() {
            return {
                darkMode: localStorage.getItem('darkMode') === 'true' || false,
                user: null,
                notifications: [],
                unreadCount: 0,

                init() {
                    // Set initial dark mode
                    if (this.darkMode) {
                        document.documentElement.classList.add('dark');
                    }

                    // Watch for dark mode changes
                    this.$watch('darkMode', (value) => {
                        localStorage.setItem('darkMode', value);
                        if (value) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    });

                    // Load user data if authenticated
                    this.loadUser();

                    // Setup notifications
                    this.setupNotifications();
                },

                async loadUser() {
                    try {
                        const response = await fetch('/api/v1/auth/me', {
                            credentials: 'include'
                        });

                        if (response.ok) {
                            this.user = await response.json();
                        }
                    } catch (error) {
                        console.log('User not authenticated');
                    }
                },

                async logout() {
                    try {
                        const response = await fetch('/api/v1/auth/logout', {
                            method: 'POST',
                            credentials: 'include'
                        });

                        if (response.ok) {
                            window.location.href = '/';
                        }
                    } catch (error) {
                        console.error('Logout error:', error);
                        // Force redirect even if logout fails
                        window.location.href = '/';
                    }
                },

                isCurrentPage(path) {
                    return window.location.pathname === path;
                },

                setupNotifications() {
                    // Setup Server-Sent Events for real-time notifications
                    if (this.user && typeof(EventSource) !== "undefined") {
                        const eventSource = new EventSource('/api/v1/notifications/stream');

                        eventSource.onmessage = (event) => {
                            const notification = JSON.parse(event.data);
                            this.addNotification(notification);
                        };

                        eventSource.onerror = (error) => {
                            console.log('SSE connection error:', error);
                            // Attempt to reconnect after 5 seconds
                            setTimeout(() => {
                                this.setupNotifications();
                            }, 5000);
                        };
                    }
                },

                addNotification(notification) {
                    this.notifications.unshift(notification);
                    this.unreadCount++;

                    // Show toast notification
                    this.showToast(notification.message, notification.type || 'info');

                    // Auto-remove after 10 notifications
                    if (this.notifications.length > 10) {
                        this.notifications = this.notifications.slice(0, 10);
                    }
                },

                showToast(message, type = 'info') {
                    const toast = document.createElement('div');
                    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
                        type === 'success' ? 'bg-green-500' :
                        type === 'error' ? 'bg-red-500' :
                        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                    } text-white transform transition-all duration-300 translate-x-full`;

                    toast.innerHTML = `
                        <div class="flex items-center">
                            <div class="flex-1">
                                <p class="text-sm font-medium">${message}</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    `;

                    document.body.appendChild(toast);

                    // Animate in
                    setTimeout(() => {
                        toast.classList.remove('translate-x-full');
                    }, 100);

                    // Auto-remove after 5 seconds
                    setTimeout(() => {
                        toast.classList.add('translate-x-full');
                        setTimeout(() => {
                            if (toast.parentElement) {
                                toast.remove();
                            }
                        }, 300);
                    }, 5000);
                }
            }
        }

        // HTMX Configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Configure HTMX
            htmx.config.globalViewTransitions = true;
            htmx.config.defaultSwapStyle = 'outerHTML';
            htmx.config.defaultSwapDelay = 0;
            htmx.config.defaultSettleDelay = 20;

            // Add loading indicators
            document.body.addEventListener('htmx:beforeRequest', function(evt) {
                const indicator = document.getElementById('loading-indicator');
                if (indicator) {
                    indicator.style.opacity = '1';
                }
            });

            document.body.addEventListener('htmx:afterRequest', function(evt) {
                const indicator = document.getElementById('loading-indicator');
                if (indicator) {
                    indicator.style.opacity = '0';
                }
            });

            // Handle HTMX errors
            document.body.addEventListener('htmx:responseError', function(evt) {
                console.error('HTMX Error:', evt.detail);
                // Show error toast
                if (window.app) {
                    window.app().showToast('An error occurred. Please try again.', 'error');
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
