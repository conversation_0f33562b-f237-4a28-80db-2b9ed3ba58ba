{% extends "base.html" %}

{% block title %}Tasks - TomoPlan{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8" x-data="taskManager()">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
                Tasks
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Manage your tasks and stay productive
            </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <button 
                @click="showCreateModal = true"
                class="btn btn-primary"
            >
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                New Task
            </button>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input 
                        type="text" 
                        x-model="searchQuery"
                        @input="debouncedSearch()"
                        placeholder="Search tasks..."
                        class="search-input"
                    >
                </div>

                <!-- Priority Filter -->
                <select x-model="priorityFilter" @change="applyFilters()" class="form-input">
                    <option value="">All Priorities</option>
                    <option value="LOW">Low</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HIGH">High</option>
                    <option value="CRITICAL">Critical</option>
                </select>

                <!-- Status Filter -->
                <select x-model="statusFilter" @change="applyFilters()" class="form-input">
                    <option value="">All Tasks</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="overdue">Overdue</option>
                </select>

                <!-- Sort -->
                <select x-model="sortBy" @change="applyFilters()" class="form-input">
                    <option value="created_at">Date Created</option>
                    <option value="due_date">Due Date</option>
                    <option value="priority">Priority</option>
                    <option value="name">Name</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="mt-6">
        <div x-show="loading" class="space-y-4">
            <div class="skeleton h-20 w-full"></div>
            <div class="skeleton h-20 w-full"></div>
            <div class="skeleton h-20 w-full"></div>
        </div>

        <div x-show="!loading && filteredTasks.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No tasks found</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <span x-show="searchQuery || priorityFilter || statusFilter">Try adjusting your filters or</span>
                Get started by creating a new task.
            </p>
            <div class="mt-6">
                <button @click="showCreateModal = true" class="btn btn-primary">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    New Task
                </button>
            </div>
        </div>

        <div x-show="!loading && filteredTasks.length > 0" class="space-y-4">
            <template x-for="task in paginatedTasks" :key="task.id">
                <div class="card hover:shadow-md transition-shadow duration-200">
                    <div class="card-body">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3 flex-1">
                                <input 
                                    type="checkbox" 
                                    :checked="task.is_done"
                                    @change="toggleTask(task.id, $event.target.checked)"
                                    class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white" 
                                        :class="{ 'line-through opacity-60': task.is_done }" 
                                        x-text="task.name">
                                    </h3>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400" 
                                       :class="{ 'line-through opacity-60': task.is_done }" 
                                       x-text="task.description">
                                    </p>
                                    <div class="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span x-text="formatDate(task.created_at)"></span>
                                        <span x-show="task.due_date" x-text="'Due: ' + formatDate(task.due_date)"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <span class="priority-badge" :class="getPriorityColor(task.priority)" x-text="task.priority"></span>
                                <div class="flex space-x-1">
                                    <button 
                                        @click="editTask(task)"
                                        class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                                        title="Edit task"
                                    >
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                    <button 
                                        @click="deleteTask(task.id)"
                                        class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                                        title="Delete task"
                                    >
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Pagination -->
        <div x-show="!loading && totalPages > 1" class="mt-6 pagination">
            <div class="pagination-info">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    Showing <span x-text="(currentPage - 1) * pageSize + 1"></span> to 
                    <span x-text="Math.min(currentPage * pageSize, filteredTasks.length)"></span> of 
                    <span x-text="filteredTasks.length"></span> results
                </p>
            </div>
            <div class="pagination-nav-desktop">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button 
                        @click="currentPage = Math.max(1, currentPage - 1)"
                        :disabled="currentPage === 1"
                        class="pagination-button rounded-l-md"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
                    >
                        Previous
                    </button>
                    
                    <template x-for="page in visiblePages" :key="page">
                        <button 
                            @click="currentPage = page"
                            class="pagination-button"
                            :class="{ 'active': page === currentPage }"
                            x-text="page"
                        ></button>
                    </template>
                    
                    <button 
                        @click="currentPage = Math.min(totalPages, currentPage + 1)"
                        :disabled="currentPage === totalPages"
                        class="pagination-button rounded-r-md"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
                    >
                        Next
                    </button>
                </nav>
            </div>
        </div>
    </div>

    <!-- Create/Edit Task Modal -->
    <div x-show="showCreateModal || showEditModal" class="modal-overlay" @click.self="closeModal()">
        <div class="modal-container max-w-lg">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white" x-text="showEditModal ? 'Edit Task' : 'Create New Task'"></h3>
                </div>
                
                <form @submit.prevent="showEditModal ? updateTask() : createTask()" class="px-6 py-4 space-y-4">
                    <div>
                        <label class="form-label">Task Name</label>
                        <input 
                            type="text" 
                            x-model="taskForm.name"
                            required
                            maxlength="100"
                            class="form-input"
                            placeholder="Enter task name"
                        >
                    </div>
                    
                    <div>
                        <label class="form-label">Description</label>
                        <textarea 
                            x-model="taskForm.description"
                            required
                            maxlength="1000"
                            rows="3"
                            class="form-input"
                            placeholder="Enter task description"
                        ></textarea>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="form-label">Priority</label>
                            <select x-model="taskForm.priority" class="form-input">
                                <option value="LOW">Low</option>
                                <option value="MEDIUM">Medium</option>
                                <option value="HIGH">High</option>
                                <option value="CRITICAL">Critical</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="form-label">Due Date (Optional)</label>
                            <input 
                                type="datetime-local" 
                                x-model="taskForm.due_date"
                                class="form-input"
                            >
                        </div>
                    </div>
                    
                    <div x-show="showEditModal">
                        <label class="flex items-center">
                            <input 
                                type="checkbox" 
                                x-model="taskForm.is_done"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Mark as completed</span>
                        </label>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" @click="closeModal()" class="btn btn-secondary">
                            Cancel
                        </button>
                        <button 
                            type="submit" 
                            :disabled="taskFormLoading"
                            class="btn btn-primary"
                            :class="{ 'opacity-50 cursor-not-allowed': taskFormLoading }"
                        >
                            <span x-text="taskFormLoading ? 'Saving...' : (showEditModal ? 'Update Task' : 'Create Task')"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='js/tasks.js') }}"></script>
{% endblock %}
