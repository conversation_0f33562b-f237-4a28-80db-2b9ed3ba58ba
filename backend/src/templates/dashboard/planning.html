{% extends "base.html" %}

{% block title %}AI Planning - TomoPlan{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8" x-data="aiPlanning()">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
                AI Planning
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Let AI help you plan and organize your daily tasks
            </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <button 
                @click="triggerAIPlanning()"
                :disabled="loading"
                class="btn btn-primary"
                :class="{ 'opacity-50 cursor-not-allowed': loading }"
            >
                <svg class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <span x-text="loading ? 'Planning...' : 'Generate AI Plan'"></span>
            </button>
        </div>
    </div>

    <!-- Planning Status -->
    <div class="mt-6" x-show="loading">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-center py-8">
                    <div class="text-center">
                        <svg class="animate-spin h-12 w-12 text-blue-500 mx-auto" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">AI is planning your day...</h3>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">This may take a few moments</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- No Planning Results -->
    <div class="mt-6" x-show="!loading && !planningResults && !roastMessage">
        <div class="card">
            <div class="card-body">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">Ready for AI Planning</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Click "Generate AI Plan" to let our AI analyze your tasks and create an optimized daily plan.
                    </p>
                    <div class="mt-6">
                        <button @click="triggerAIPlanning()" class="btn btn-primary">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            Generate AI Plan
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Roast Message (when no tasks) -->
    <div class="mt-6" x-show="!loading && roastMessage">
        <div class="card border-l-4 border-orange-500">
            <div class="card-body">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-orange-800 dark:text-orange-200">
                            AI Roast 🔥
                        </h3>
                        <div class="mt-2 text-sm text-orange-700 dark:text-orange-300">
                            <p x-text="roastMessage" class="italic"></p>
                        </div>
                        <div class="mt-4">
                            <a href="/tasks" class="btn btn-primary">
                                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Add Some Tasks
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Planning Results -->
    <div class="mt-6" x-show="!loading && planningResults && planningResults.length > 0">
        <div class="space-y-6">
            <template x-for="(task, index) in planningResults" :key="index">
                <div class="card">
                    <div class="card-header">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="priority-badge" :class="getPriorityColor(task.task_priority)" x-text="task.task_priority"></span>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white" x-text="task.task_name"></h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    Est. <span x-text="task.task_stimation"></span>
                                </span>
                                <button 
                                    @click="acceptTask(task)"
                                    class="btn btn-sm btn-primary"
                                >
                                    Accept Task
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-gray-700 dark:text-gray-300 mb-4" x-text="task.task_description"></p>
                        
                        <div x-show="task.tip" class="mb-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">AI Tip</h4>
                                    <p class="mt-1 text-sm text-blue-700 dark:text-blue-300" x-text="task.tip"></p>
                                </div>
                            </div>
                        </div>
                        
                        <div x-show="task.task_breakdown && task.task_breakdown.length > 0">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Task Breakdown:</h4>
                            <ul class="space-y-2">
                                <template x-for="(step, stepIndex) in task.task_breakdown" :key="stepIndex">
                                    <li class="flex items-start space-x-2">
                                        <span class="flex-shrink-0 w-5 h-5 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium" x-text="stepIndex + 1"></span>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white" x-text="step.todo_name"></p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400" x-text="step.todo_description"></p>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>
                </div>
            </template>
            
            <!-- Accept All Tasks -->
            <div class="card bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700">
                <div class="card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-green-800 dark:text-green-200">
                                Accept All Planned Tasks
                            </h3>
                            <p class="text-sm text-green-600 dark:text-green-300">
                                Add all AI-planned tasks to your task list at once
                            </p>
                        </div>
                        <button 
                            @click="acceptAllTasks()"
                            :disabled="acceptingTasks"
                            class="btn btn-success"
                            :class="{ 'opacity-50 cursor-not-allowed': acceptingTasks }"
                        >
                            <span x-text="acceptingTasks ? 'Adding Tasks...' : 'Accept All Tasks'"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Planning History -->
    <div class="mt-8" x-show="!loading && planningHistory.length > 0">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Recent Planning Sessions
                </h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <template x-for="session in planningHistory" :key="session.id">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white" x-text="session.summary"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="formatDate(session.created_at)"></p>
                            </div>
                            <button 
                                @click="viewPlanningSession(session)"
                                class="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                View Details
                            </button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='js/planning.js') }}"></script>
{% endblock %}
