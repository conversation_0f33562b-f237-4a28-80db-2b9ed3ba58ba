{% extends "base.html" %}

{% block title %}Analytics - TomoPlan{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8" x-data="analytics()">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
                Analytics
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Track your productivity and task completion trends
            </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <select x-model="timeRange" @change="loadAnalytics()" class="form-input">
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
            </select>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Total Tasks
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="summary.totalTasks || 0">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Completion Rate
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="summary.completionRate || '0%'">
                                0%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Avg. Daily Tasks
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="summary.avgDailyTasks || 0">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Productivity Score
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white" x-text="summary.productivityScore || 0">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Grid -->
    <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Task Priority Distribution -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Task Priority Distribution
                </h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="priorityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Completion Trends -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Completion Trends
                </h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="completionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Daily Productivity -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Daily Productivity
                </h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="productivityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Task Status Overview -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Task Status Overview
                </h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Detailed Statistics
                </h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    <!-- Most Productive Day -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Most Productive Day</h4>
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400" x-text="stats.mostProductiveDay || 'N/A'">N/A</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400" x-text="stats.mostProductiveDayTasks ? stats.mostProductiveDayTasks + ' tasks completed' : ''"></p>
                    </div>

                    <!-- Average Completion Time -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Avg. Completion Time</h4>
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400" x-text="stats.avgCompletionTime || 'N/A'">N/A</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Per task</p>
                    </div>

                    <!-- Streak -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Current Streak</h4>
                        <p class="text-2xl font-bold text-purple-600 dark:text-purple-400" x-text="stats.currentStreak || 0">0</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Days with completed tasks</p>
                    </div>

                    <!-- Priority Breakdown -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Priority Breakdown</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-red-600 dark:text-red-400">Critical:</span>
                                <span x-text="stats.priorityBreakdown?.CRITICAL || 0"></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-orange-600 dark:text-orange-400">High:</span>
                                <span x-text="stats.priorityBreakdown?.HIGH || 0"></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-yellow-600 dark:text-yellow-400">Medium:</span>
                                <span x-text="stats.priorityBreakdown?.MEDIUM || 0"></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-green-600 dark:text-green-400">Low:</span>
                                <span x-text="stats.priorityBreakdown?.LOW || 0"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Weekly Summary -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">This Week</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span>Created:</span>
                                <span x-text="stats.weeklyStats?.created || 0"></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Completed:</span>
                                <span x-text="stats.weeklyStats?.completed || 0"></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Overdue:</span>
                                <span x-text="stats.weeklyStats?.overdue || 0"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Summary -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">This Month</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span>Created:</span>
                                <span x-text="stats.monthlyStats?.created || 0"></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Completed:</span>
                                <span x-text="stats.monthlyStats?.completed || 0"></span>
                            </div>
                            <div class="flex justify-between">
                                <span>Success Rate:</span>
                                <span x-text="stats.monthlyStats?.successRate || '0%'"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='js/charts.js') }}"></script>
<script src="{{ url_for('static', path='js/analytics.js') }}"></script>
{% endblock %}
