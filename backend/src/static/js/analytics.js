// Analytics Alpine.js component
function analytics() {
    return {
        loading: true,
        timeRange: 30,
        chartManager: null,
        
        // Data
        summary: {
            totalTasks: 0,
            completionRate: '0%',
            avgDailyTasks: 0,
            productivityScore: 0
        },
        
        stats: {
            mostProductiveDay: null,
            mostProductiveDayTasks: 0,
            avgCompletionTime: null,
            currentStreak: 0,
            priorityBreakdown: {
                CRITICAL: 0,
                HIGH: 0,
                MEDIUM: 0,
                LOW: 0
            },
            weeklyStats: {
                created: 0,
                completed: 0,
                overdue: 0
            },
            monthlyStats: {
                created: 0,
                completed: 0,
                successRate: '0%'
            }
        },
        
        chartData: {
            priority: { CRITICAL: 0, HIGH: 0, MEDIUM: 0, LOW: 0 },
            completion: { labels: [], completed: [], created: [] },
            productivity: { weeklyProductivity: [0, 0, 0, 0, 0, 0, 0] },
            status: { completed: 0, inProgress: 0, overdue: 0, pending: 0 }
        },
        
        init() {
            this.chartManager = new ChartManager();
            this.loadAnalytics();
        },
        
        async loadAnalytics() {
            this.loading = true;
            
            try {
                await Promise.all([
                    this.loadTasks(),
                    this.loadSummary(),
                    this.loadDetailedStats()
                ]);
                
                // Initialize charts after data is loaded
                this.$nextTick(() => {
                    this.initializeCharts();
                });
                
            } catch (error) {
                console.error('Error loading analytics:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to load analytics data',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        },
        
        async loadTasks() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/tasks/tasks?limit=1000&page=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const tasks = await response.json();
                    this.processTaskData(tasks);
                } else {
                    throw new Error('Failed to fetch tasks');
                }
            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        },
        
        processTaskData(tasks) {
            const now = new Date();
            const timeRangeDate = new Date(now.getTime() - (this.timeRange * 24 * 60 * 60 * 1000));
            
            // Filter tasks by time range
            const filteredTasks = tasks.filter(task => 
                new Date(task.created_at) >= timeRangeDate
            );
            
            // Calculate summary statistics
            const completedTasks = filteredTasks.filter(task => task.is_done);
            const totalTasks = filteredTasks.length;
            const completionRate = totalTasks > 0 ? Math.round((completedTasks.length / totalTasks) * 100) : 0;
            const avgDailyTasks = Math.round(totalTasks / this.timeRange);
            
            this.summary = {
                totalTasks,
                completionRate: `${completionRate}%`,
                avgDailyTasks,
                productivityScore: this.calculateProductivityScore(filteredTasks)
            };
            
            // Process priority distribution
            this.chartData.priority = {
                CRITICAL: filteredTasks.filter(t => t.priority === 'CRITICAL').length,
                HIGH: filteredTasks.filter(t => t.priority === 'HIGH').length,
                MEDIUM: filteredTasks.filter(t => t.priority === 'MEDIUM').length,
                LOW: filteredTasks.filter(t => t.priority === 'LOW').length
            };
            
            // Process completion trends
            this.processCompletionTrends(filteredTasks);
            
            // Process weekly productivity
            this.processWeeklyProductivity(filteredTasks);
            
            // Process status overview
            this.processStatusOverview(filteredTasks);
            
            // Update detailed stats
            this.updateDetailedStats(filteredTasks);
        },
        
        processCompletionTrends(tasks) {
            const days = this.timeRange;
            const labels = [];
            const completed = [];
            const created = [];
            
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                
                labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
                
                const dayTasks = tasks.filter(task => 
                    task.created_at.split('T')[0] === dateStr
                );
                
                const dayCompleted = tasks.filter(task => 
                    task.is_done && task.created_at.split('T')[0] <= dateStr
                ).length;
                
                created.push(dayTasks.length);
                completed.push(dayCompleted);
            }
            
            this.chartData.completion = { labels, completed, created };
        },
        
        processWeeklyProductivity(tasks) {
            const weeklyData = [0, 0, 0, 0, 0, 0, 0]; // Mon-Sun
            
            tasks.filter(task => task.is_done).forEach(task => {
                const date = new Date(task.created_at);
                const dayOfWeek = (date.getDay() + 6) % 7; // Convert to Mon=0, Sun=6
                weeklyData[dayOfWeek]++;
            });
            
            this.chartData.productivity = { weeklyProductivity: weeklyData };
        },
        
        processStatusOverview(tasks) {
            const now = new Date();
            
            const completed = tasks.filter(task => task.is_done).length;
            const overdue = tasks.filter(task => 
                !task.is_done && task.due_date && new Date(task.due_date) < now
            ).length;
            const pending = tasks.filter(task => !task.is_done).length;
            const inProgress = pending - overdue;
            
            this.chartData.status = { completed, inProgress, overdue, pending };
        },
        
        updateDetailedStats(tasks) {
            // Most productive day
            const dailyCompletion = {};
            tasks.filter(task => task.is_done).forEach(task => {
                const date = new Date(task.created_at).toLocaleDateString();
                dailyCompletion[date] = (dailyCompletion[date] || 0) + 1;
            });
            
            const mostProductiveEntry = Object.entries(dailyCompletion)
                .sort(([,a], [,b]) => b - a)[0];
            
            if (mostProductiveEntry) {
                this.stats.mostProductiveDay = mostProductiveEntry[0];
                this.stats.mostProductiveDayTasks = mostProductiveEntry[1];
            }
            
            // Priority breakdown
            this.stats.priorityBreakdown = { ...this.chartData.priority };
            
            // Weekly and monthly stats
            const now = new Date();
            const weekStart = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
            const monthStart = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            
            const weeklyTasks = tasks.filter(task => new Date(task.created_at) >= weekStart);
            const monthlyTasks = tasks.filter(task => new Date(task.created_at) >= monthStart);
            
            this.stats.weeklyStats = {
                created: weeklyTasks.length,
                completed: weeklyTasks.filter(task => task.is_done).length,
                overdue: weeklyTasks.filter(task => 
                    !task.is_done && task.due_date && new Date(task.due_date) < now
                ).length
            };
            
            const monthlyCompleted = monthlyTasks.filter(task => task.is_done).length;
            const monthlySuccessRate = monthlyTasks.length > 0 
                ? Math.round((monthlyCompleted / monthlyTasks.length) * 100) 
                : 0;
            
            this.stats.monthlyStats = {
                created: monthlyTasks.length,
                completed: monthlyCompleted,
                successRate: `${monthlySuccessRate}%`
            };
            
            // Calculate current streak
            this.stats.currentStreak = this.calculateStreak(tasks);
        },
        
        calculateProductivityScore(tasks) {
            if (tasks.length === 0) return 0;
            
            const completed = tasks.filter(task => task.is_done).length;
            const completionRate = completed / tasks.length;
            
            const priorityWeights = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
            const weightedCompleted = tasks
                .filter(task => task.is_done)
                .reduce((sum, task) => sum + priorityWeights[task.priority], 0);
            
            const maxPossibleWeight = tasks
                .reduce((sum, task) => sum + priorityWeights[task.priority], 0);
            
            const weightedScore = maxPossibleWeight > 0 ? weightedCompleted / maxPossibleWeight : 0;
            
            return Math.round((completionRate * 0.6 + weightedScore * 0.4) * 100);
        },
        
        calculateStreak(tasks) {
            const completedTasks = tasks
                .filter(task => task.is_done)
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            
            if (completedTasks.length === 0) return 0;
            
            let streak = 0;
            const today = new Date();
            
            for (let i = 0; i < 30; i++) { // Check last 30 days
                const checkDate = new Date(today.getTime() - (i * 24 * 60 * 60 * 1000));
                const dateStr = checkDate.toISOString().split('T')[0];
                
                const hasCompletedTask = completedTasks.some(task => 
                    task.created_at.split('T')[0] === dateStr
                );
                
                if (hasCompletedTask) {
                    streak++;
                } else if (i > 0) { // Don't break on today if no tasks
                    break;
                }
            }
            
            return streak;
        },
        
        async loadSummary() {
            // Additional summary data could be loaded from API
            // For now, this is handled in processTaskData
        },
        
        async loadDetailedStats() {
            // Additional detailed stats could be loaded from API
            // For now, this is handled in updateDetailedStats
        },
        
        initializeCharts() {
            // Initialize all charts
            const priorityCtx = document.getElementById('priorityChart');
            const completionCtx = document.getElementById('completionChart');
            const productivityCtx = document.getElementById('productivityChart');
            const statusCtx = document.getElementById('statusChart');
            
            if (priorityCtx) {
                this.chartManager.createPriorityChart(priorityCtx, this.chartData.priority);
            }
            
            if (completionCtx) {
                this.chartManager.createCompletionChart(completionCtx, this.chartData.completion);
            }
            
            if (productivityCtx) {
                this.chartManager.createProductivityChart(productivityCtx, this.chartData.productivity);
            }
            
            if (statusCtx) {
                this.chartManager.createStatusChart(statusCtx, this.chartData.status);
            }
        },
        
        showNotification(notification) {
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: notification
            }));
        }
    };
}

// Register Alpine.js component
document.addEventListener('alpine:init', () => {
    Alpine.data('analytics', analytics);
});
