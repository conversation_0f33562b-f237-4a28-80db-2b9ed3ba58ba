// Dashboard Alpine.js component
function dashboard() {
    return {
        loading: true,
        stats: {
            total: 0,
            completed: 0,
            pending: 0,
            overdue: 0
        },
        recentTasks: [],
        
        init() {
            this.loadDashboardData();
        },
        
        async loadDashboardData() {
            this.loading = true;
            
            try {
                await Promise.all([
                    this.loadStats(),
                    this.loadRecentTasks()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to load dashboard data',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        },
        
        async loadStats() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/tasks/tasks?limit=1000&page=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const tasks = await response.json();
                    this.calculateStats(tasks);
                } else {
                    throw new Error('Failed to fetch tasks');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        },
        
        async loadRecentTasks() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/tasks/tasks?limit=5&page=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    this.recentTasks = await response.json();
                } else {
                    throw new Error('Failed to fetch recent tasks');
                }
            } catch (error) {
                console.error('Error loading recent tasks:', error);
                this.recentTasks = [];
            }
        },
        
        calculateStats(tasks) {
            const now = new Date();
            
            this.stats = {
                total: tasks.length,
                completed: tasks.filter(task => task.is_done).length,
                pending: tasks.filter(task => !task.is_done).length,
                overdue: tasks.filter(task => {
                    if (!task.due_date || task.is_done) return false;
                    return new Date(task.due_date) < now;
                }).length
            };
        },
        
        async toggleTask(taskId, isCompleted) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/v1/tasks/task/${taskId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        is_done: isCompleted
                    })
                });
                
                if (response.ok) {
                    // Update the task in the local array
                    const taskIndex = this.recentTasks.findIndex(task => task.id === taskId);
                    if (taskIndex !== -1) {
                        this.recentTasks[taskIndex].is_done = isCompleted;
                    }
                    
                    // Refresh stats
                    await this.loadStats();
                    
                    this.showNotification({
                        title: 'Success',
                        message: `Task ${isCompleted ? 'completed' : 'reopened'} successfully`,
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to update task');
                }
            } catch (error) {
                console.error('Error toggling task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to update task status',
                    type: 'error'
                });
                
                // Revert the checkbox state
                const taskIndex = this.recentTasks.findIndex(task => task.id === taskId);
                if (taskIndex !== -1) {
                    this.recentTasks[taskIndex].is_done = !isCompleted;
                }
            }
        },
        
        async refreshData() {
            await this.loadDashboardData();
            this.showNotification({
                title: 'Success',
                message: 'Dashboard data refreshed',
                type: 'success'
            });
        },
        
        showNotification(notification) {
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: notification
            }));
        },
        
        formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
                return 'Today';
            } else if (diffDays === 2) {
                return 'Yesterday';
            } else if (diffDays <= 7) {
                return `${diffDays - 1} days ago`;
            } else {
                return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
                });
            }
        },
        
        getPriorityColor(priority) {
            const colors = {
                'LOW': 'priority-low',
                'MEDIUM': 'priority-medium',
                'HIGH': 'priority-high',
                'CRITICAL': 'priority-critical'
            };
            return colors[priority] || colors['LOW'];
        }
    };
}

// Register Alpine.js component
document.addEventListener('alpine:init', () => {
    Alpine.data('dashboard', dashboard);
});
