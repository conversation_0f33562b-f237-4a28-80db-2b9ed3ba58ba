// Tasks management Alpine.js component
function taskManager() {
    return {
        // Data
        tasks: [],
        filteredTasks: [],
        loading: true,
        
        // Filters
        searchQuery: '',
        priorityFilter: '',
        statusFilter: '',
        sortBy: 'created_at',
        
        // Pagination
        currentPage: 1,
        pageSize: 10,
        
        // Modals
        showCreateModal: false,
        showEditModal: false,
        taskFormLoading: false,
        
        // Task form
        taskForm: {
            id: null,
            name: '',
            description: '',
            priority: 'MEDIUM',
            due_date: '',
            is_done: false
        },
        
        // Debounced search
        searchTimeout: null,
        
        init() {
            this.loadTasks();
            this.debouncedSearch = this.debounce(this.applyFilters.bind(this), 300);
        },
        
        async loadTasks() {
            this.loading = true;
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/tasks/tasks?limit=1000&page=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    this.tasks = await response.json();
                    this.applyFilters();
                } else {
                    throw new Error('Failed to fetch tasks');
                }
            } catch (error) {
                console.error('Error loading tasks:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to load tasks',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        },
        
        applyFilters() {
            let filtered = [...this.tasks];
            
            // Search filter
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(task => 
                    task.name.toLowerCase().includes(query) ||
                    task.description.toLowerCase().includes(query)
                );
            }
            
            // Priority filter
            if (this.priorityFilter) {
                filtered = filtered.filter(task => task.priority === this.priorityFilter);
            }
            
            // Status filter
            if (this.statusFilter) {
                const now = new Date();
                filtered = filtered.filter(task => {
                    switch (this.statusFilter) {
                        case 'completed':
                            return task.is_done;
                        case 'pending':
                            return !task.is_done;
                        case 'overdue':
                            return !task.is_done && task.due_date && new Date(task.due_date) < now;
                        default:
                            return true;
                    }
                });
            }
            
            // Sort
            filtered.sort((a, b) => {
                switch (this.sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'priority':
                        const priorityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
                        return priorityOrder[b.priority] - priorityOrder[a.priority];
                    case 'due_date':
                        if (!a.due_date && !b.due_date) return 0;
                        if (!a.due_date) return 1;
                        if (!b.due_date) return -1;
                        return new Date(a.due_date) - new Date(b.due_date);
                    case 'created_at':
                    default:
                        return new Date(b.created_at) - new Date(a.created_at);
                }
            });
            
            this.filteredTasks = filtered;
            this.currentPage = 1; // Reset to first page when filters change
        },
        
        async toggleTask(taskId, isCompleted) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/v1/tasks/task/${taskId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        is_done: isCompleted
                    })
                });
                
                if (response.ok) {
                    // Update the task in the local array
                    const taskIndex = this.tasks.findIndex(task => task.id === taskId);
                    if (taskIndex !== -1) {
                        this.tasks[taskIndex].is_done = isCompleted;
                        this.applyFilters();
                    }
                    
                    this.showNotification({
                        title: 'Success',
                        message: `Task ${isCompleted ? 'completed' : 'reopened'} successfully`,
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to update task');
                }
            } catch (error) {
                console.error('Error toggling task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to update task status',
                    type: 'error'
                });
                
                // Revert the checkbox state
                const taskIndex = this.tasks.findIndex(task => task.id === taskId);
                if (taskIndex !== -1) {
                    this.tasks[taskIndex].is_done = !isCompleted;
                    this.applyFilters();
                }
            }
        },
        
        async createTask() {
            this.taskFormLoading = true;
            
            try {
                const token = localStorage.getItem('token');
                const taskData = {
                    name: this.taskForm.name,
                    description: this.taskForm.description,
                    priority: this.taskForm.priority,
                    is_done: false
                };
                
                if (this.taskForm.due_date) {
                    taskData.due_date = new Date(this.taskForm.due_date).toISOString();
                }
                
                const response = await fetch('/api/v1/tasks/addtask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(taskData)
                });
                
                if (response.ok) {
                    const newTask = await response.json();
                    this.tasks.unshift(newTask);
                    this.applyFilters();
                    this.closeModal();
                    
                    this.showNotification({
                        title: 'Success',
                        message: 'Task created successfully',
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to create task');
                }
            } catch (error) {
                console.error('Error creating task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to create task',
                    type: 'error'
                });
            } finally {
                this.taskFormLoading = false;
            }
        },
        
        editTask(task) {
            this.taskForm = {
                id: task.id,
                name: task.name,
                description: task.description,
                priority: task.priority,
                due_date: task.due_date ? new Date(task.due_date).toISOString().slice(0, 16) : '',
                is_done: task.is_done
            };
            this.showEditModal = true;
        },
        
        async updateTask() {
            this.taskFormLoading = true;
            
            try {
                const token = localStorage.getItem('token');
                const taskData = {
                    name: this.taskForm.name,
                    description: this.taskForm.description,
                    priority: this.taskForm.priority,
                    is_done: this.taskForm.is_done
                };
                
                const response = await fetch(`/api/v1/tasks/task/${this.taskForm.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(taskData)
                });
                
                if (response.ok) {
                    const updatedTask = await response.json();
                    const taskIndex = this.tasks.findIndex(task => task.id === this.taskForm.id);
                    if (taskIndex !== -1) {
                        this.tasks[taskIndex] = updatedTask;
                        this.applyFilters();
                    }
                    this.closeModal();
                    
                    this.showNotification({
                        title: 'Success',
                        message: 'Task updated successfully',
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to update task');
                }
            } catch (error) {
                console.error('Error updating task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to update task',
                    type: 'error'
                });
            } finally {
                this.taskFormLoading = false;
            }
        },
        
        async deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) {
                return;
            }
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/v1/tasks/${taskId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    this.tasks = this.tasks.filter(task => task.id !== taskId);
                    this.applyFilters();
                    
                    this.showNotification({
                        title: 'Success',
                        message: 'Task deleted successfully',
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to delete task');
                }
            } catch (error) {
                console.error('Error deleting task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to delete task',
                    type: 'error'
                });
            }
        },
        
        closeModal() {
            this.showCreateModal = false;
            this.showEditModal = false;
            this.taskForm = {
                id: null,
                name: '',
                description: '',
                priority: 'MEDIUM',
                due_date: '',
                is_done: false
            };
        },
        
        // Computed properties
        get paginatedTasks() {
            const start = (this.currentPage - 1) * this.pageSize;
            const end = start + this.pageSize;
            return this.filteredTasks.slice(start, end);
        },
        
        get totalPages() {
            return Math.ceil(this.filteredTasks.length / this.pageSize);
        },
        
        get visiblePages() {
            const pages = [];
            const total = this.totalPages;
            const current = this.currentPage;
            
            if (total <= 7) {
                for (let i = 1; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                if (current <= 4) {
                    for (let i = 1; i <= 5; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                } else if (current >= total - 3) {
                    pages.push(1);
                    pages.push('...');
                    for (let i = total - 4; i <= total; i++) {
                        pages.push(i);
                    }
                } else {
                    pages.push(1);
                    pages.push('...');
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                    pages.push(total);
                }
            }
            
            return pages;
        },
        
        // Utility methods
        debounce(func, wait) {
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(this.searchTimeout);
                    func(...args);
                };
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(later, wait);
            };
        },
        
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },
        
        getPriorityColor(priority) {
            const colors = {
                'LOW': 'priority-low',
                'MEDIUM': 'priority-medium',
                'HIGH': 'priority-high',
                'CRITICAL': 'priority-critical'
            };
            return colors[priority] || colors['LOW'];
        },
        
        showNotification(notification) {
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: notification
            }));
        }
    };
}

// Register Alpine.js component
document.addEventListener('alpine:init', () => {
    Alpine.data('taskManager', taskManager);
});
