// Notifications Alpine.js component
function notifications() {
    return {
        notifications: [],
        nextId: 1,
        
        init() {
            // Listen for notification events
            document.addEventListener('notification:show', (event) => {
                this.addNotification(event.detail);
            });
        },
        
        addNotification(notification) {
            const id = this.nextId++;
            const newNotification = {
                id,
                title: notification.title || 'Notification',
                message: notification.message || '',
                type: notification.type || 'info',
                show: true,
                timestamp: new Date()
            };
            
            this.notifications.unshift(newNotification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                this.removeNotification(id);
            }, 5000);
        },
        
        removeNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications[index].show = false;
                // Remove from array after animation
                setTimeout(() => {
                    this.notifications.splice(index, 1);
                }, 300);
            }
        },
        
        clearAll() {
            this.notifications.forEach(notification => {
                notification.show = false;
            });
            setTimeout(() => {
                this.notifications = [];
            }, 300);
        },
        
        getNotificationIcon(type) {
            const icons = {
                success: `<svg class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>`,
                error: `<svg class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>`,
                warning: `<svg class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>`,
                info: `<svg class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>`
            };
            return icons[type] || icons.info;
        },
        
        getNotificationColor(type) {
            const colors = {
                success: 'border-green-500 bg-green-50 dark:bg-green-900',
                error: 'border-red-500 bg-red-50 dark:bg-red-900',
                warning: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900',
                info: 'border-blue-500 bg-blue-50 dark:bg-blue-900'
            };
            return colors[type] || colors.info;
        }
    };
}

// Task notifications component
function taskNotifications() {
    return {
        eventSource: null,
        connected: false,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
        
        init() {
            this.connect();
        },
        
        connect() {
            if (!localStorage.getItem('token')) {
                return;
            }
            
            try {
                this.eventSource = new EventSource('/api/v1/notifications/stream');
                
                this.eventSource.onopen = () => {
                    this.connected = true;
                    this.reconnectAttempts = 0;
                    console.log('SSE connection established');
                };
                
                this.eventSource.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleNotification(data);
                    } catch (error) {
                        console.error('Error parsing SSE message:', error);
                    }
                };
                
                this.eventSource.onerror = (error) => {
                    console.error('SSE connection error:', error);
                    this.connected = false;
                    this.handleReconnect();
                };
                
            } catch (error) {
                console.error('Failed to establish SSE connection:', error);
                this.handleReconnect();
            }
        },
        
        handleNotification(data) {
            // Determine notification type based on content
            let type = 'info';
            let title = 'Notification';
            
            if (data.includes('Reminder')) {
                type = 'warning';
                title = 'Task Reminder';
            } else if (data.includes('completed') || data.includes('success')) {
                type = 'success';
                title = 'Success';
            } else if (data.includes('error') || data.includes('failed')) {
                type = 'error';
                title = 'Error';
            }
            
            // Show notification
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: {
                    title: title,
                    message: data,
                    type: type
                }
            }));
            
            // Update unread count
            this.updateUnreadCount();
        },
        
        handleReconnect() {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
                
                console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
                
                setTimeout(() => {
                    this.connect();
                }, delay);
            } else {
                console.error('Max reconnection attempts reached');
                document.dispatchEvent(new CustomEvent('notification:show', {
                    detail: {
                        title: 'Connection Error',
                        message: 'Unable to connect to notification service. Please refresh the page.',
                        type: 'error'
                    }
                }));
            }
        },
        
        disconnect() {
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
                this.connected = false;
            }
        },
        
        updateUnreadCount() {
            // This would typically update a counter in the UI
            // For now, we'll just dispatch an event
            document.dispatchEvent(new CustomEvent('notifications:unread-updated'));
        }
    };
}

// Notification history component
function notificationHistory() {
    return {
        notifications: [],
        loading: false,
        page: 1,
        hasMore: true,
        
        init() {
            this.loadNotifications();
        },
        
        async loadNotifications() {
            this.loading = true;
            
            try {
                // This would typically fetch from an API endpoint
                // For now, we'll simulate with localStorage
                const stored = localStorage.getItem('notification_history');
                if (stored) {
                    this.notifications = JSON.parse(stored);
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
            } finally {
                this.loading = false;
            }
        },
        
        markAsRead(id) {
            const notification = this.notifications.find(n => n.id === id);
            if (notification) {
                notification.read = true;
                this.saveNotifications();
            }
        },
        
        markAllAsRead() {
            this.notifications.forEach(n => n.read = true);
            this.saveNotifications();
        },
        
        deleteNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications.splice(index, 1);
                this.saveNotifications();
            }
        },
        
        clearAll() {
            this.notifications = [];
            this.saveNotifications();
        },
        
        saveNotifications() {
            localStorage.setItem('notification_history', JSON.stringify(this.notifications));
        },
        
        getUnreadCount() {
            return this.notifications.filter(n => !n.read).length;
        },
        
        formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) { // Less than 1 minute
                return 'Just now';
            } else if (diff < 3600000) { // Less than 1 hour
                return `${Math.floor(diff / 60000)} minutes ago`;
            } else if (diff < 86400000) { // Less than 1 day
                return `${Math.floor(diff / 3600000)} hours ago`;
            } else {
                return date.toLocaleDateString();
            }
        }
    };
}

// Register Alpine.js components
document.addEventListener('alpine:init', () => {
    Alpine.data('notifications', notifications);
    Alpine.data('taskNotifications', taskNotifications);
    Alpine.data('notificationHistory', notificationHistory);
});
