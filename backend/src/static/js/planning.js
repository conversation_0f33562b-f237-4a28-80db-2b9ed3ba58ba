// AI Planning Alpine.js component
function aiPlanning() {
    return {
        loading: false,
        planningResults: null,
        roastMessage: null,
        acceptingTasks: false,
        planningHistory: [],
        
        init() {
            this.loadPlanningHistory();
        },
        
        async triggerAIPlanning() {
            this.loading = true;
            this.planningResults = null;
            this.roastMessage = null;
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/agent/todoes', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    
                    // Check if result is a string (roast message) or array (tasks)
                    if (typeof result === 'string') {
                        this.roastMessage = result;
                        this.planningResults = null;
                    } else if (Array.isArray(result)) {
                        this.planningResults = result;
                        this.roastMessage = null;
                        
                        // Save to planning history
                        this.savePlanningSession(result);
                    } else {
                        throw new Error('Unexpected response format');
                    }
                    
                    this.showNotification({
                        title: 'Success',
                        message: 'AI planning completed successfully',
                        type: 'success'
                    });
                } else {
                    throw new Error('Failed to get AI planning');
                }
            } catch (error) {
                console.error('Error triggering AI planning:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to generate AI plan. Please try again.',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        },
        
        async acceptTask(task) {
            try {
                const token = localStorage.getItem('token');
                const taskData = {
                    name: task.task_name,
                    description: task.task_description,
                    priority: task.task_priority,
                    is_done: false
                };
                
                const response = await fetch('/api/v1/tasks/addtask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(taskData)
                });
                
                if (response.ok) {
                    this.showNotification({
                        title: 'Success',
                        message: `Task "${task.task_name}" added successfully`,
                        type: 'success'
                    });
                    
                    // Remove the task from planning results
                    this.planningResults = this.planningResults.filter(t => t !== task);
                } else {
                    throw new Error('Failed to create task');
                }
            } catch (error) {
                console.error('Error accepting task:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to add task. Please try again.',
                    type: 'error'
                });
            }
        },
        
        async acceptAllTasks() {
            if (!this.planningResults || this.planningResults.length === 0) {
                return;
            }
            
            this.acceptingTasks = true;
            
            try {
                const token = localStorage.getItem('token');
                const tasksData = this.planningResults.map(task => ({
                    name: task.task_name,
                    description: task.task_description,
                    priority: task.task_priority,
                    is_done: false
                }));
                
                const response = await fetch('/api/v1/tasks/addtask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(tasksData)
                });
                
                if (response.ok) {
                    this.showNotification({
                        title: 'Success',
                        message: `${this.planningResults.length} tasks added successfully`,
                        type: 'success'
                    });
                    
                    // Clear planning results
                    this.planningResults = null;
                    
                    // Redirect to tasks page
                    setTimeout(() => {
                        window.location.href = '/tasks';
                    }, 1500);
                } else {
                    throw new Error('Failed to create tasks');
                }
            } catch (error) {
                console.error('Error accepting all tasks:', error);
                this.showNotification({
                    title: 'Error',
                    message: 'Failed to add tasks. Please try again.',
                    type: 'error'
                });
            } finally {
                this.acceptingTasks = false;
            }
        },
        
        savePlanningSession(results) {
            const session = {
                id: Date.now(),
                summary: `Generated ${results.length} task${results.length !== 1 ? 's' : ''}`,
                results: results,
                created_at: new Date().toISOString()
            };
            
            // Get existing history from localStorage
            const existingHistory = JSON.parse(localStorage.getItem('planning_history') || '[]');
            
            // Add new session to the beginning
            existingHistory.unshift(session);
            
            // Keep only the last 10 sessions
            const limitedHistory = existingHistory.slice(0, 10);
            
            // Save back to localStorage
            localStorage.setItem('planning_history', JSON.stringify(limitedHistory));
            
            // Update component state
            this.planningHistory = limitedHistory;
        },
        
        loadPlanningHistory() {
            try {
                const history = JSON.parse(localStorage.getItem('planning_history') || '[]');
                this.planningHistory = history;
            } catch (error) {
                console.error('Error loading planning history:', error);
                this.planningHistory = [];
            }
        },
        
        viewPlanningSession(session) {
            // Show the planning results from the session
            this.planningResults = session.results;
            this.roastMessage = null;
            
            this.showNotification({
                title: 'Planning Session Loaded',
                message: `Viewing session from ${this.formatDate(session.created_at)}`,
                type: 'info'
            });
        },
        
        getPriorityColor(priority) {
            const colors = {
                'LOW': 'priority-low',
                'MEDIUM': 'priority-medium',
                'HIGH': 'priority-high',
                'CRITICAL': 'priority-critical'
            };
            return colors[priority] || colors['LOW'];
        },
        
        formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
                return 'Today at ' + date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } else if (diffDays === 2) {
                return 'Yesterday at ' + date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } else if (diffDays <= 7) {
                return `${diffDays - 1} days ago`;
            } else {
                return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
                });
            }
        },
        
        showNotification(notification) {
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: notification
            }));
        }
    };
}

// Register Alpine.js component
document.addEventListener('alpine:init', () => {
    Alpine.data('aiPlanning', aiPlanning);
});
