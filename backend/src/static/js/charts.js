// Chart.js configurations and utilities
class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            }
        };
    }

    // Create priority distribution chart (Doughnut)
    createPriorityChart(ctx, data) {
        if (this.charts.priority) {
            this.charts.priority.destroy();
        }

        this.charts.priority = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Critical', 'High', 'Medium', 'Low'],
                datasets: [{
                    data: [
                        data.CRITICAL || 0,
                        data.HIGH || 0,
                        data.MEDIUM || 0,
                        data.LOW || 0
                    ],
                    backgroundColor: [
                        '#ef4444', // red-500
                        '#f97316', // orange-500
                        '#eab308', // yellow-500
                        '#22c55e'  // green-500
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        return this.charts.priority;
    }

    // Create completion trends chart (Line)
    createCompletionChart(ctx, data) {
        if (this.charts.completion) {
            this.charts.completion.destroy();
        }

        this.charts.completion = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'Tasks Completed',
                    data: data.completed || [],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Tasks Created',
                    data: data.created || [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });

        return this.charts.completion;
    }

    // Create daily productivity chart (Bar)
    createProductivityChart(ctx, data) {
        if (this.charts.productivity) {
            this.charts.productivity.destroy();
        }

        this.charts.productivity = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Tasks Completed',
                    data: data.weeklyProductivity || [0, 0, 0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
                        '#8b5cf6', '#06b6d4', '#84cc16'
                    ],
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    legend: {
                        display: false
                    }
                }
            }
        });

        return this.charts.productivity;
    }

    // Create task status overview chart (Polar Area)
    createStatusChart(ctx, data) {
        if (this.charts.status) {
            this.charts.status.destroy();
        }

        this.charts.status = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: ['Completed', 'In Progress', 'Overdue', 'Pending'],
                datasets: [{
                    data: [
                        data.completed || 0,
                        data.inProgress || 0,
                        data.overdue || 0,
                        data.pending || 0
                    ],
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',   // green
                        'rgba(59, 130, 246, 0.8)',  // blue
                        'rgba(239, 68, 68, 0.8)',   // red
                        'rgba(156, 163, 175, 0.8)'  // gray
                    ],
                    borderColor: [
                        '#22c55e',
                        '#3b82f6',
                        '#ef4444',
                        '#9ca3af'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    r: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        return this.charts.status;
    }

    // Update chart data
    updateChart(chartName, newData) {
        const chart = this.charts[chartName];
        if (!chart) return;

        switch (chartName) {
            case 'priority':
                chart.data.datasets[0].data = [
                    newData.CRITICAL || 0,
                    newData.HIGH || 0,
                    newData.MEDIUM || 0,
                    newData.LOW || 0
                ];
                break;
            case 'completion':
                chart.data.labels = newData.labels || [];
                chart.data.datasets[0].data = newData.completed || [];
                chart.data.datasets[1].data = newData.created || [];
                break;
            case 'productivity':
                chart.data.datasets[0].data = newData.weeklyProductivity || [0, 0, 0, 0, 0, 0, 0];
                break;
            case 'status':
                chart.data.datasets[0].data = [
                    newData.completed || 0,
                    newData.inProgress || 0,
                    newData.overdue || 0,
                    newData.pending || 0
                ];
                break;
        }

        chart.update();
    }

    // Destroy all charts
    destroyAll() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }

    // Get chart instance
    getChart(name) {
        return this.charts[name];
    }

    // Check if chart exists
    hasChart(name) {
        return !!this.charts[name];
    }
}

// Export for use in other files
window.ChartManager = ChartManager;
