// Main Alpine.js application
function app() {
    return {
        // Authentication state
        isAuthenticated: false,
        user: null,
        token: null,
        
        // UI state
        sidebarOpen: false,
        currentPage: window.location.pathname,
        unreadCount: 0,
        
        // Initialize the app
        init() {
            this.checkAuth();
            this.setupHTMX();
            this.connectSSE();
            
            // Listen for HTMX events
            document.addEventListener('htmx:afterRequest', (event) => {
                this.handleHTMXResponse(event);
            });
            
            // Listen for auth events
            document.addEventListener('auth:login', (event) => {
                this.handleLogin(event.detail);
            });
            
            document.addEventListener('auth:logout', () => {
                this.handleLogout();
            });
        },
        
        // Check authentication status
        checkAuth() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            if (token && user) {
                this.token = token;
                this.user = JSON.parse(user);
                this.isAuthenticated = true;
                
                // Set authorization header for HTMX requests
                document.body.setAttribute('hx-headers', JSON.stringify({
                    'Authorization': `Bearer ${token}`
                }));
            } else {
                this.redirectToLogin();
            }
        },
        
        // Setup HTMX configuration
        setupHTMX() {
            // Configure HTMX
            htmx.config.globalViewTransitions = true;
            htmx.config.defaultSwapStyle = 'outerHTML';
            htmx.config.defaultSwapDelay = 100;
            htmx.config.defaultSettleDelay = 100;
            
            // Add loading indicator
            htmx.config.indicatorClass = 'htmx-indicator';
        },
        
        // Connect to Server-Sent Events
        connectSSE() {
            if (!this.isAuthenticated) return;
            
            const eventSource = new EventSource('/api/v1/notifications/stream');
            
            eventSource.onmessage = (event) => {
                try {
                    const notification = JSON.parse(event.data);
                    this.showNotification(notification);
                    this.unreadCount++;
                } catch (error) {
                    console.error('Error parsing SSE message:', error);
                }
            };
            
            eventSource.onerror = (error) => {
                console.error('SSE connection error:', error);
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    if (this.isAuthenticated) {
                        this.connectSSE();
                    }
                }, 5000);
            };
            
            // Store reference for cleanup
            this.eventSource = eventSource;
        },
        
        // Handle HTMX responses
        handleHTMXResponse(event) {
            const xhr = event.detail.xhr;
            
            // Handle authentication errors
            if (xhr.status === 401) {
                this.handleLogout();
                return;
            }
            
            // Handle success responses
            if (xhr.status >= 200 && xhr.status < 300) {
                // Show success message if provided
                const successMessage = xhr.getResponseHeader('X-Success-Message');
                if (successMessage) {
                    this.showNotification({
                        title: 'Success',
                        message: successMessage,
                        type: 'success'
                    });
                }
            }
            
            // Handle error responses
            if (xhr.status >= 400) {
                const errorMessage = xhr.getResponseHeader('X-Error-Message') || 'An error occurred';
                this.showNotification({
                    title: 'Error',
                    message: errorMessage,
                    type: 'error'
                });
            }
        },
        
        // Handle login
        handleLogin(data) {
            this.token = data.access_token;
            this.user = { id: data.id };
            this.isAuthenticated = true;
            
            // Store in localStorage
            localStorage.setItem('token', data.access_token);
            localStorage.setItem('user', JSON.stringify(this.user));
            
            // Set authorization header
            document.body.setAttribute('hx-headers', JSON.stringify({
                'Authorization': `Bearer ${data.access_token}`
            }));
            
            // Connect to SSE
            this.connectSSE();
            
            // Redirect to dashboard
            window.location.href = '/dashboard';
        },
        
        // Handle logout
        handleLogout() {
            this.token = null;
            this.user = null;
            this.isAuthenticated = false;
            
            // Clear localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Close SSE connection
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }
            
            // Remove authorization header
            document.body.removeAttribute('hx-headers');
            
            this.redirectToLogin();
        },
        
        // Logout function
        logout() {
            this.handleLogout();
        },
        
        // Redirect to login
        redirectToLogin() {
            if (!['/login', '/register', '/'].includes(window.location.pathname)) {
                window.location.href = '/login';
            }
        },
        
        // Show notification
        showNotification(notification) {
            // Dispatch event for notification component
            document.dispatchEvent(new CustomEvent('notification:show', {
                detail: notification
            }));
        },
        
        // Check if current page
        isCurrentPage(path) {
            return this.currentPage === path;
        },
        
        // Toggle sidebar (for mobile)
        toggleSidebar() {
            this.sidebarOpen = !this.sidebarOpen;
        },
        
        // Format date
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },
        
        // Format time
        formatTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },
        
        // Get priority color
        getPriorityColor(priority) {
            const colors = {
                'LOW': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                'MEDIUM': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                'HIGH': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
                'CRITICAL': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            };
            return colors[priority] || colors['LOW'];
        },
        
        // Get priority badge color
        getPriorityBadgeColor(priority) {
            const colors = {
                'LOW': 'bg-green-500',
                'MEDIUM': 'bg-yellow-500',
                'HIGH': 'bg-orange-500',
                'CRITICAL': 'bg-red-500'
            };
            return colors[priority] || colors['LOW'];
        }
    };
}

// Global utility functions
window.utils = {
    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Copy to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('Failed to copy text: ', err);
            return false;
        }
    }
};

// Initialize Alpine.js data
document.addEventListener('alpine:init', () => {
    Alpine.data('app', app);
});
