// Authentication related Alpine.js components

// Login form component
function loginForm() {
    return {
        email: '',
        password: '',
        loading: false,
        errors: {},
        
        async submitLogin() {
            this.loading = true;
            this.errors = {};
            
            try {
                const formData = new FormData();
                formData.append('username', this.email);
                formData.append('password', this.password);
                
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Dispatch login event
                    document.dispatchEvent(new CustomEvent('auth:login', {
                        detail: data
                    }));
                } else {
                    this.errors.general = data.detail || 'Login failed';
                }
            } catch (error) {
                this.errors.general = 'Network error. Please try again.';
            } finally {
                this.loading = false;
            }
        },
        
        validateEmail() {
            if (!this.email) {
                this.errors.email = 'Email is required';
            } else if (!/\S+@\S+\.\S+/.test(this.email)) {
                this.errors.email = 'Email is invalid';
            } else {
                delete this.errors.email;
            }
        },
        
        validatePassword() {
            if (!this.password) {
                this.errors.password = 'Password is required';
            } else if (this.password.length < 6) {
                this.errors.password = 'Password must be at least 6 characters';
            } else {
                delete this.errors.password;
            }
        }
    };
}

// Register form component
function registerForm() {
    return {
        email: '',
        password: '',
        confirmPassword: '',
        firstName: '',
        lastName: '',
        loading: false,
        errors: {},
        
        async submitRegister() {
            this.loading = true;
            this.errors = {};
            
            // Validate form
            this.validateForm();
            
            if (Object.keys(this.errors).length > 0) {
                this.loading = false;
                return;
            }
            
            try {
                const response = await fetch('/api/v1/auth/sign up', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: this.email,
                        password: this.password,
                        first_name: this.firstName,
                        last_name: this.lastName
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Show success message and redirect to login
                    document.dispatchEvent(new CustomEvent('notification:show', {
                        detail: {
                            title: 'Success',
                            message: 'Account created successfully! Please log in.',
                            type: 'success'
                        }
                    }));
                    
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    this.errors.general = data.detail || 'Registration failed';
                }
            } catch (error) {
                this.errors.general = 'Network error. Please try again.';
            } finally {
                this.loading = false;
            }
        },
        
        validateForm() {
            // Email validation
            if (!this.email) {
                this.errors.email = 'Email is required';
            } else if (!/\S+@\S+\.\S+/.test(this.email)) {
                this.errors.email = 'Email is invalid';
            }
            
            // Password validation
            if (!this.password) {
                this.errors.password = 'Password is required';
            } else if (this.password.length < 6) {
                this.errors.password = 'Password must be at least 6 characters';
            }
            
            // Confirm password validation
            if (!this.confirmPassword) {
                this.errors.confirmPassword = 'Please confirm your password';
            } else if (this.password !== this.confirmPassword) {
                this.errors.confirmPassword = 'Passwords do not match';
            }
            
            // First name validation
            if (!this.firstName) {
                this.errors.firstName = 'First name is required';
            }
            
            // Last name validation
            if (!this.lastName) {
                this.errors.lastName = 'Last name is required';
            }
        },
        
        validateEmail() {
            if (!this.email) {
                this.errors.email = 'Email is required';
            } else if (!/\S+@\S+\.\S+/.test(this.email)) {
                this.errors.email = 'Email is invalid';
            } else {
                delete this.errors.email;
            }
        },
        
        validatePassword() {
            if (!this.password) {
                this.errors.password = 'Password is required';
            } else if (this.password.length < 6) {
                this.errors.password = 'Password must be at least 6 characters';
            } else {
                delete this.errors.password;
            }
            
            // Re-validate confirm password if it exists
            if (this.confirmPassword) {
                this.validateConfirmPassword();
            }
        },
        
        validateConfirmPassword() {
            if (!this.confirmPassword) {
                this.errors.confirmPassword = 'Please confirm your password';
            } else if (this.password !== this.confirmPassword) {
                this.errors.confirmPassword = 'Passwords do not match';
            } else {
                delete this.errors.confirmPassword;
            }
        }
    };
}

// Profile form component
function profileForm() {
    return {
        email: '',
        firstName: '',
        lastName: '',
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
        loading: false,
        passwordLoading: false,
        errors: {},
        passwordErrors: {},
        
        init() {
            this.loadUserData();
        },
        
        loadUserData() {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            this.email = user.email || '';
            this.firstName = user.first_name || '';
            this.lastName = user.last_name || '';
        },
        
        async updateProfile() {
            this.loading = true;
            this.errors = {};
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/auth/me', {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        email: this.email,
                        first_name: this.firstName,
                        last_name: this.lastName
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Update stored user data
                    const user = JSON.parse(localStorage.getItem('user') || '{}');
                    user.email = this.email;
                    user.first_name = this.firstName;
                    user.last_name = this.lastName;
                    localStorage.setItem('user', JSON.stringify(user));
                    
                    document.dispatchEvent(new CustomEvent('notification:show', {
                        detail: {
                            title: 'Success',
                            message: 'Profile updated successfully!',
                            type: 'success'
                        }
                    }));
                } else {
                    this.errors.general = data.detail || 'Update failed';
                }
            } catch (error) {
                this.errors.general = 'Network error. Please try again.';
            } finally {
                this.loading = false;
            }
        },
        
        async updatePassword() {
            this.passwordLoading = true;
            this.passwordErrors = {};
            
            // Validate passwords
            if (!this.currentPassword) {
                this.passwordErrors.currentPassword = 'Current password is required';
            }
            if (!this.newPassword) {
                this.passwordErrors.newPassword = 'New password is required';
            } else if (this.newPassword.length < 6) {
                this.passwordErrors.newPassword = 'Password must be at least 6 characters';
            }
            if (this.newPassword !== this.confirmNewPassword) {
                this.passwordErrors.confirmNewPassword = 'Passwords do not match';
            }
            
            if (Object.keys(this.passwordErrors).length > 0) {
                this.passwordLoading = false;
                return;
            }
            
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/v1/auth/me/password', {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        current_password: this.currentPassword,
                        new_password: this.newPassword
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Clear password fields
                    this.currentPassword = '';
                    this.newPassword = '';
                    this.confirmNewPassword = '';
                    
                    document.dispatchEvent(new CustomEvent('notification:show', {
                        detail: {
                            title: 'Success',
                            message: 'Password updated successfully!',
                            type: 'success'
                        }
                    }));
                } else {
                    this.passwordErrors.general = data.detail || 'Password update failed';
                }
            } catch (error) {
                this.passwordErrors.general = 'Network error. Please try again.';
            } finally {
                this.passwordLoading = false;
            }
        }
    };
}

// Register Alpine.js components
document.addEventListener('alpine:init', () => {
    Alpine.data('loginForm', loginForm);
    Alpine.data('registerForm', registerForm);
    Alpine.data('profileForm', profileForm);
});
