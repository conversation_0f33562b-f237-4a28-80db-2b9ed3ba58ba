/* Custom CSS for TomoPlan */

/* Base styles */
* {
    box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
}

/* Navigation links */
.nav-link {
    @apply inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-200;
}

.nav-link.active {
    @apply border-blue-500 text-blue-600 dark:text-blue-400;
}

/* Form styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
}

.form-input.error {
    @apply border-red-300 text-red-900 placeholder-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:border-red-600 dark:text-red-400 dark:placeholder-red-400;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

.form-error {
    @apply mt-1 text-sm text-red-600 dark:text-red-400;
}

/* Button styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
    @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
    @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600;
}

.btn-danger {
    @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-success {
    @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

/* Card styles */
.card {
    @apply bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700;
}

.card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
    @apply px-4 py-5 sm:p-6;
}

.card-footer {
    @apply px-4 py-4 sm:px-6 border-t border-gray-200 dark:border-gray-700;
}

/* Task priority badges */
.priority-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.priority-low {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.priority-medium {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.priority-high {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200;
}

.priority-critical {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Task status */
.task-completed {
    @apply opacity-60 line-through;
}

/* Loading states */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}

.htmx-request.htmx-indicator {
    display: flex;
}

/* Skeleton loading */
.skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.skeleton-text {
    @apply h-4 bg-gray-200 dark:bg-gray-700 rounded;
}

.skeleton-avatar {
    @apply h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full;
}

/* Modal styles */
.modal-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
    @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800;
}

/* Dropdown styles */
.dropdown-menu {
    @apply absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50;
}

.dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer;
}

/* Toast notifications */
.toast-enter {
    @apply transform translate-x-full opacity-0;
}

.toast-enter-active {
    @apply transition-all duration-300 ease-out;
}

.toast-enter-to {
    @apply transform translate-x-0 opacity-100;
}

.toast-leave {
    @apply transform translate-x-0 opacity-100;
}

.toast-leave-active {
    @apply transition-all duration-200 ease-in;
}

.toast-leave-to {
    @apply transform translate-x-full opacity-0;
}

/* Chart container */
.chart-container {
    @apply relative h-64 w-full;
}

/* Responsive table */
.table-responsive {
    @apply overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg;
}

.table {
    @apply min-w-full divide-y divide-gray-300 dark:divide-gray-700;
}

.table-header {
    @apply bg-gray-50 dark:bg-gray-800;
}

.table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.table-body {
    @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
}

.table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-150;
}

.table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}

/* Search input */
.search-input {
    @apply block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:text-white;
}

/* Pagination */
.pagination {
    @apply flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6;
}

.pagination-info {
    @apply text-sm text-gray-700 dark:text-gray-300;
}

.pagination-nav {
    @apply flex-1 flex justify-between sm:hidden;
}

.pagination-nav-desktop {
    @apply hidden sm:flex-1 sm:flex sm:items-center sm:justify-between;
}

.pagination-button {
    @apply relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600;
}

.pagination-button.active {
    @apply z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-400;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Focus styles for accessibility */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-before: always;
    }
}

/* Mobile-specific styles */
@media (max-width: 640px) {
    .mobile-full {
        @apply w-full;
    }
    
    .mobile-hidden {
        @apply hidden;
    }
    
    .mobile-stack {
        @apply flex-col space-y-2;
    }
}
