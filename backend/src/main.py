from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse, Response, HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles

# FastHX will be handled manually with decorators
from pathlib import Path
from limits.storage import RedisStorage
from loguru import logger
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address
from src.templating import jinja

from src.agent.router import router as agent_router
from src.auth.router import router as auth_router
from src.config import settings
from src.database import session
from src.initdb import init
from src.midlewares import LoguruExceptionMiddleware, RateLimitMiddleware
from src.notifications.router import router as notif_router
from src.tasks.router import router as tasks_router
from src.notifications.scheduler import start_scheduler


@asynccontextmanager
async def lifespan(_: FastAPI):
    start_scheduler()
    # Initialize tables

    try:
        await init()

    except Exception as e:
        print(f"Failed to initialize database: {e}")
        raise

    # Connect to database
    try:
        await session.connect()
        print("Database connected successfully")
    except Exception as e:
        print(f"Failed to connect to database: {e}")
        raise

    try:
        yield
    finally:
        # Disconnect from database
        try:
            await session.disconnect()
            print("Database disconnected successfully")
        except Exception as e:
            print(f"Error disconnecting from database: {e}")


DESCRIPTION = """
TomoPlan is an AI-powered task management application designed to help you optimize your daily productivity.

Key Features:
- 📝 Task Management: Create, update, and delete tasks with priority levels (LOW, MEDIUM, HIGH, CRITICAL)
- 🤖 AI Assistant: AI-powered task planning and optimization
- 📱 Real-time Notifications: Stay updated with task reminders and progress
- 🔄 Task Planning: Plan your daily tasks using AI recommendations
- 🔐 Secure Authentication: Protected API endpoints with JWT authentication

The API is organized into several key components:

1. Authentication (`/auth`)
   - User registration and login
   - Password management
   - Profile updates

2. Tasks (`/tasks`)
   - CRUD operations for tasks
   - Task prioritization
   - Task filtering and pagination
   - Task status updates

3. AI Agent (`/agent`)
   - AI-powered task planning
   - Task breakdown and optimization
   - Daily task recommendations

4. Notifications (`/notifications`)
   - Real-time notification streaming
   - Task reminder system
   - Progress updates

Security:
- All endpoints require JWT authentication (except auth endpoints)
- CORS enabled for frontend communication
- Secure password hashing

Environment:
- Local development: http://localhost:8000
- Production: Configurable via environment variables

For more information about specific endpoints, use the interactive API documentation available at `/docs` and `/redoc`
"""

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.API_V1_STR,
    description=DESCRIPTION,
    lifespan=lifespan,
    contact={
        "name": "TomoPlan Support",
        "url": "https://github.com/Mohamed-Rirash/TomoPlan",
        "email": "<EMAIL>",
    },
    license_info={"name": "MIT", "url": "https://opensource.org/licenses/MIT"},
)

# Setup static files
BASE_DIR = Path(__file__).resolve().parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"

app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Templates available for manual FastHX decorator usage


app.add_middleware(RateLimitMiddleware, rate="5/minute")
# ✅ Add the middleware
# app.add_middleware(LoguruExceptionMiddleware)

# ✅ Optional: configure Loguru
# logger.add(
# "logs/error.log", level="ERROR", rotation="500 KB", retention="7 days", enqueue=True
# )


def get_cors_origins() -> list[str]:
    if isinstance(settings.BACKEND_CORS_ORIGINS, str):
        return [origin.strip() for origin in settings.BACKEND_CORS_ORIGINS.split(",")]
    return [str(origin) for origin in settings.BACKEND_CORS_ORIGINS]


app.add_middleware(
    CORSMiddleware,
    allow_origins=get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_class=HTMLResponse)
@jinja.page("index.html")
def read_root(request: Request):

  ...

# UI Routes - HTML template responses using FastHX decorators
@app.get("/login", response_class=HTMLResponse)
@jinja.hx("auth/login.html")
async def login_page(request: Request):
    """Serve the login page"""
    ...

@app.get("/register", response_class=HTMLResponse)
@jinja.hx("auth/register.html")
async def register_page(request: Request):
    """Serve the registration page"""
    ...

@app.get("/dashboard", response_class=HTMLResponse)
@jinja.hx("dashboard/index.html")
async def dashboard_page(request: Request):
    """Serve the main dashboard"""
    ...

@app.get("/tasks", response_class=HTMLResponse)
@jinja.hx("dashboard/tasks.html")
async def tasks_page(request: Request):
    """Serve the tasks management page"""
    ...

@app.get("/planning", response_class=HTMLResponse)
@jinja.hx("dashboard/planning.html")
async def planning_page(request: Request):
    """Serve the AI planning page"""
    ...

@app.get("/analytics", response_class=HTMLResponse)
@jinja.hx("dashboard/analytics.html")
async def analytics_page(request: Request):
    """Serve the analytics page"""
    ...

@app.get("/profile", response_class=HTMLResponse)
@jinja.hx("auth/profile.html")
async def profile_page(request: Request):
    """Serve the user profile page"""
    ...

@app.get("/password-reset", response_class=HTMLResponse)
@jinja.hx("auth/password-reset.html")
async def password_reset_page(request: Request):
    """Serve the password reset page"""
    ...

@app.get("/api/health")
def health_check():
    return {"message": "TomoPlan API is running!", "status": "healthy"}

@app.get("/favicon.ico")
def favicon():
    """Handle favicon requests to avoid 404s"""
    return Response(status_code=204)  # No Content


app.include_router(auth_router, prefix=f"{settings.API_V1_STR}")
app.include_router(tasks_router, prefix=f"{settings.API_V1_STR}")
app.include_router(notif_router, prefix=f"{settings.API_V1_STR}")
app.include_router(agent_router, prefix=f"{settings.API_V1_STR}")
