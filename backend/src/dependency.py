import uuid
from collections.abc import Generator
from typing import Annotated, Optional

import jwt
from databases import Database
from fastapi import Depends, HTTPException, Request, <PERSON>ie
from fastapi.security import OAuth2PasswordBearer

from src.auth.services import get_session_by_token, update_session_access, get_user_by_id
from src.auth.security import verify_jwt_token
from src.config import settings
from src.database import session

# OAuth2 for API endpoints (legacy JWT support)
reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/login",
    auto_error=False  # Don't auto-error, we'll handle it manually
)


def get_db() -> Generator[Database, None, None]:
    yield session


db_dependency = Annotated[Database, Depends(get_db)]
token_dependency = Annotated[str, Depends(reusable_oauth2)]


async def get_current_user_from_session(
    request: Request,
    db: db_dependency,
    session_token: Optional[str] = <PERSON><PERSON>(None, alias="session_token")
) -> Optional[dict]:
    """Get current user from session cookie"""
    if not session_token:
        return None

    try:
        # Get session from database
        session_data = await get_session_by_token(session_token, db)
        if not session_data:
            return None

        # Update session access time
        await update_session_access(session_data.id, db)

        # Return user data
        return {
            "id": session_data.user_id,
            "email": session_data.email,
            "first_name": session_data.first_name,
            "last_name": session_data.last_name,
            "is_active": session_data.is_active,
            "is_verified": session_data.is_verified,
            "last_login": session_data.last_login,
            "created_at": session_data.created_at,
            "session_id": session_data.id
        }
    except Exception:
        return None


async def get_current_user_from_jwt(
    db: db_dependency,
    token: Optional[str] = Depends(reusable_oauth2),
) -> Optional[dict]:
    """Get current user from JWT token (for API endpoints)"""
    if not token:
        return None

    try:
        payload = verify_jwt_token(token)
        if not payload:
            return None

        user_id = uuid.UUID(payload["sub"])
        user = await get_user_by_id(user_id, db)

        if not user:
            return None

        return {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "last_login": user.last_login,
            "created_at": user.created_at,
        }
    except Exception:
        return None


async def get_current_user(
    request: Request,
    db: db_dependency,
    session_token: Optional[str] = Cookie(None, alias="session_token"),
    jwt_token: Optional[str] = Depends(reusable_oauth2),
) -> dict:
    """Get current user from session cookie or JWT token"""
    # Try session-based auth first (for UI)
    user = await get_current_user_from_session(request, db, session_token)

    # Fallback to JWT auth (for API)
    if not user:
        user = await get_current_user_from_jwt(db, jwt_token)

    if not user:
        raise HTTPException(
            status_code=401,
            detail="Not authenticated"
        )

    if not user["is_active"]:
        raise HTTPException(
            status_code=401,
            detail="Account is deactivated"
        )

    return user


async def get_optional_current_user(
    request: Request,
    db: db_dependency,
    session_token: Optional[str] = Cookie(None, alias="session_token"),
    jwt_token: Optional[str] = Depends(reusable_oauth2),
) -> Optional[dict]:
    """Get current user if authenticated, None otherwise"""
    try:
        return await get_current_user(request, db, session_token, jwt_token)
    except HTTPException:
        return None


async def require_verified_user(
    user: dict = Depends(get_current_user)
) -> dict:
    """Require user to be verified"""
    if not user["is_verified"]:
        raise HTTPException(
            status_code=403,
            detail="Email verification required"
        )
    return user


# Type annotations for dependencies
user_dependency = Annotated[dict, Depends(get_current_user)]
optional_user_dependency = Annotated[Optional[dict], Depends(get_optional_current_user)]
verified_user_dependency = Annotated[dict, Depends(require_verified_user)]

# Legacy dependency name for backward compatibility
user_dependecy = user_dependency
