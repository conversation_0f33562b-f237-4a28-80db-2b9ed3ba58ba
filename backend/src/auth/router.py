from datetime import <PERSON><PERSON><PERSON>
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Form
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse

from src.auth.schemas import (
    LoginRequest,
    LoginResponse,
    LogoutResponse,
    UpdatePassword,
    UserRegister,
    UserUpdateMe,
    UserRead,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest,
    EmailVerificationConfirm,
    UserSessions,
    JWTLoginResponse,
)
from src.auth.security import (
    create_access_token,
    get_password_hash,
    verify_password,
    get_client_ip,
    get_user_agent,
)
from src.auth.services import (
    authenticate_user,
    create_user,
    get_user_by_email,
    update_current_user,
    update_current_user_password,
    create_user_session,
    invalidate_session,
    invalidate_all_user_sessions,
    get_user_sessions,
    create_password_reset_token,
    use_password_reset_token,
    create_email_verification_token,
    confirm_email_verification,
    get_user_by_id,
)
from src.config import settings
from src.dependency import (
    db_dependency,
    user_dependency,
    optional_user_dependency,
    verified_user_dependency
)

router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
)


# Session-based Authentication Endpoints (for UI)
@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register_user(
    request: Request,
    data: UserRegister,
    db: db_dependency
):
    """Register new user account"""
    try:
        user = await create_user(data, db)

        # TODO: Send email verification email
        # await send_verification_email(user.email, verification_token)

        return {
            "success": True,
            "message": "Account created successfully! Please check your email to verify your account.",
            "user_id": str(user.id)
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create account"
        )


@router.post("/login", response_model=LoginResponse)
async def login_user(
    request: Request,
    response: Response,
    db: db_dependency,
    email: str = Form(...),
    password: str = Form(...),
    remember_me: bool = Form(False)
):
    """Login user with session-based authentication"""
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)

    # Authenticate user
    user, error_message = await authenticate_user(email, password, db, ip_address)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=error_message or "Invalid email or password"
        )

    # Create session
    try:
        session_token, refresh_token = await create_user_session(
            user.id, db, ip_address, user_agent, remember_me
        )

        # Set HTTP-only cookies
        max_age = 30 * 24 * 60 * 60 if remember_me else 24 * 60 * 60  # 30 days or 24 hours

        response.set_cookie(
            key="session_token",
            value=session_token,
            max_age=max_age,
            httponly=True,
            secure=settings.ENVIRONMENT != "local",  # HTTPS only in production
            samesite="lax",
            path="/"
        )

        if refresh_token:
            response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                max_age=max_age * 2,  # Refresh token lasts longer
                httponly=True,
                secure=settings.ENVIRONMENT != "local",
                samesite="lax",
                path="/auth/refresh"
            )

        user_data = UserRead(
            id=str(user.id),
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_verified=user.is_verified,
            last_login=user.last_login,
            created_at=user.created_at
        )

        return LoginResponse(
            success=True,
            message="Login successful",
            user=user_data,
            redirect_url="/dashboard"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create session"
        )


@router.post("/logout", response_model=LogoutResponse)
async def logout_user(
    request: Request,
    response: Response,
    user: optional_user_dependency,
    db: db_dependency
):
    """Logout user and invalidate session"""
    session_token = request.cookies.get("session_token")

    if session_token:
        try:
            await invalidate_session(session_token, db)
        except Exception:
            pass  # Continue even if session invalidation fails

    # Clear cookies
    response.delete_cookie(key="session_token", path="/")
    response.delete_cookie(key="refresh_token", path="/auth/refresh")

    return LogoutResponse(
        success=True,
        message="Logged out successfully",
        redirect_url="/"
    )


@router.post("/logout-all")
async def logout_all_sessions(
    user: user_dependency,
    db: db_dependency
):
    """Logout from all sessions"""
    try:
        await invalidate_all_user_sessions(user["id"], db)
        return {"success": True, "message": "Logged out from all sessions"}
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout from all sessions"
        )


# User Profile Management
@router.get("/me", response_model=UserRead)
async def get_current_user_profile(user: user_dependency):
    """Get current user profile"""
    return UserRead(
        id=str(user["id"]),
        email=user["email"],
        first_name=user["first_name"],
        last_name=user["last_name"],
        is_active=user["is_active"],
        is_verified=user["is_verified"],
        last_login=user.get("last_login"),
        created_at=user["created_at"]
    )


@router.patch("/me")
async def update_user_profile(
    data: UserUpdateMe,
    user: user_dependency,
    db: db_dependency
):
    """Update current user profile"""
    try:
        return await update_current_user(user["id"], data, db)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


@router.patch("/me/password")
async def update_user_password(
    data: UpdatePassword,
    user: user_dependency,
    db: db_dependency
):
    """Update user password"""
    # Get full user data to verify current password
    user_data = await get_user_by_id(user["id"], db)
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Verify current password
    if not verify_password(data.current_password, user_data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Current password is incorrect"
        )

    # Check if new password is different
    if verify_password(data.new_password, user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password must be different from current password"
        )

    try:
        hashed_password = get_password_hash(data.new_password)
        return await update_current_user_password(user["id"], hashed_password, db)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )


# Session Management
@router.get("/sessions", response_model=UserSessions)
async def get_user_sessions_list(
    user: user_dependency,
    db: db_dependency
):
    """Get all active sessions for current user"""
    try:
        sessions = await get_user_sessions(user["id"], db)
        current_session_id = user.get("session_id")

        session_list = []
        for session in sessions:
            session_list.append({
                "id": str(session.id),
                "ip_address": session.ip_address,
                "user_agent": session.user_agent,
                "created_at": session.created_at,
                "last_accessed": session.last_accessed,
                "is_current": session.id == current_session_id
            })

        return UserSessions(sessions=session_list)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: str,
    user: user_dependency,
    db: db_dependency
):
    """Revoke a specific session"""
    try:
        # TODO: Add validation that session belongs to user
        # For now, we'll invalidate all sessions except current
        await invalidate_all_user_sessions(user["id"], db, except_session_id=user.get("session_id"))
        return {"success": True, "message": "Session revoked"}
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke session"
        )


# Password Reset
@router.post("/password-reset/request")
async def request_password_reset(
    data: PasswordResetRequest,
    db: db_dependency
):
    """Request password reset token"""
    try:
        token = await create_password_reset_token(data.email, db)

        # TODO: Send password reset email
        # await send_password_reset_email(data.email, token)

        # Always return success to prevent email enumeration
        return {
            "success": True,
            "message": "If an account with this email exists, you will receive a password reset link."
        }
    except Exception:
        # Always return success to prevent email enumeration
        return {
            "success": True,
            "message": "If an account with this email exists, you will receive a password reset link."
        }


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    data: PasswordResetConfirm,
    db: db_dependency
):
    """Confirm password reset with token"""
    try:
        result = await use_password_reset_token(data.token, data.new_password, db)
        return {
            "success": True,
            "message": "Password reset successfully. You can now login with your new password."
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )


# Email Verification
@router.post("/email/verify/request")
async def request_email_verification(
    user: user_dependency,
    db: db_dependency
):
    """Request new email verification token"""
    try:
        token = await create_email_verification_token(user["id"], db)

        # TODO: Send verification email
        # await send_verification_email(user["email"], token)

        return {
            "success": True,
            "message": "Verification email sent. Please check your inbox."
        }
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email"
        )


@router.post("/email/verify/confirm")
async def confirm_email_verification(
    data: EmailVerificationConfirm,
    db: db_dependency
):
    """Confirm email verification with token"""
    try:
        result = await confirm_email_verification(data.token, db)
        return {
            "success": True,
            "message": "Email verified successfully!"
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify email"
        )


# Legacy JWT API Endpoints (for API compatibility)
@router.post("/api/login", response_model=JWTLoginResponse)
async def api_login(
    db: db_dependency,
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """Legacy JWT login for API access"""
    user, error_message = await authenticate_user(form_data.username, form_data.password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=error_message or "Invalid email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = await create_access_token(user.id, access_token_expires)

    return JWTLoginResponse(
        access_token=access_token,
        token_type="bearer",
        id=str(user.id),
        exp=settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    )


@router.post("/api/register", status_code=status.HTTP_201_CREATED)
async def api_register(
    data: UserRegister,
    db: db_dependency
):
    """Legacy API registration endpoint"""
    try:
        user = await create_user(data, db)
        return {"message": "User created successfully", "user_id": str(user.id)}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create account"
        )
