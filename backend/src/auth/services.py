import uuid
from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy import select, update, delete, and_, or_

from src.auth.models import (
    users,
    user_sessions,
    password_reset_tokens,
    email_verification_tokens
)
from src.auth.schemas import UserReg<PERSON>, UserUpdateMe
from src.auth.security import (
    get_password_hash,
    verify_password,
    generate_session_tokens,
    hash_token,
    verify_token_hash,
    generate_secure_token
)
from src.config import settings


async def get_user_by_email(email: str, db):
    """Get user by email address"""
    query = select(users).where(users.c.email == email.lower())
    result = await db.fetch_one(query)
    return result


async def get_user_by_id(user_id: uuid.UUID, db):
    """Get user by ID"""
    query = select(users).where(users.c.id == user_id)
    result = await db.fetch_one(query)
    return result


async def create_user(user: UserRegister, db):
    """Create new user account"""
    user_id = uuid.uuid4()

    # Check if user already exists
    existing_user = await get_user_by_email(user.email, db)
    if existing_user:
        raise ValueError("User with this email already exists")

    query = users.insert().values(
        id=user_id,
        first_name=user.first_name.strip(),
        last_name=user.last_name.strip(),
        email=user.email.lower(),
        password=get_password_hash(user.password),
        is_active=True,
        is_verified=False,  # Require email verification
        password_changed_at=datetime.now(timezone.utc),
    )

    await db.execute(query)

    # Create email verification token
    await create_email_verification_token(user_id, db)

    return await get_user_by_id(user_id, db)


async def authenticate_user(email: str, password: str, db, ip_address: str = None) -> Tuple[Optional[dict], Optional[str]]:
    """Authenticate user with enhanced security checks"""
    user = await get_user_by_email(email, db)
    if not user:
        return None, "Invalid email or password"

    # Check if account is locked
    if user.locked_until and user.locked_until > datetime.now(timezone.utc):
        return None, f"Account is locked until {user.locked_until.strftime('%Y-%m-%d %H:%M:%S UTC')}"

    # Check if account is active
    if not user.is_active:
        return None, "Account is deactivated"

    # Verify password
    if not verify_password(password, user.password):
        # Increment failed login attempts
        await increment_failed_login_attempts(user.id, db)
        return None, "Invalid email or password"

    # Reset failed login attempts on successful login
    await reset_failed_login_attempts(user.id, db)

    # Update last login
    await update_last_login(user.id, db)

    return user, None


async def increment_failed_login_attempts(user_id: uuid.UUID, db):
    """Increment failed login attempts and lock account if necessary"""
    query = select(users.c.failed_login_attempts).where(users.c.id == user_id)
    result = await db.fetch_one(query)

    if result:
        attempts = result.failed_login_attempts + 1
        locked_until = None

        # Lock account after 5 failed attempts for 30 minutes
        if attempts >= 5:
            locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)

        update_query = (
            update(users)
            .where(users.c.id == user_id)
            .values(
                failed_login_attempts=attempts,
                locked_until=locked_until
            )
        )
        await db.execute(update_query)


async def reset_failed_login_attempts(user_id: uuid.UUID, db):
    """Reset failed login attempts on successful login"""
    query = (
        update(users)
        .where(users.c.id == user_id)
        .values(
            failed_login_attempts=0,
            locked_until=None
        )
    )
    await db.execute(query)


async def update_last_login(user_id: uuid.UUID, db):
    """Update user's last login timestamp"""
    query = (
        update(users)
        .where(users.c.id == user_id)
        .values(last_login=datetime.now(timezone.utc))
    )
    await db.execute(query)


async def create_user_session(
    user_id: uuid.UUID,
    db,
    ip_address: str = None,
    user_agent: str = None,
    remember_me: bool = False
) -> Tuple[str, str]:
    """Create new user session and return session token"""
    # Generate session tokens
    session_token, session_hash, refresh_token, refresh_hash = generate_session_tokens()

    # Set expiration times
    if remember_me:
        session_expires = datetime.now(timezone.utc) + timedelta(days=30)
        refresh_expires = datetime.now(timezone.utc) + timedelta(days=60)
    else:
        session_expires = datetime.now(timezone.utc) + timedelta(hours=24)
        refresh_expires = datetime.now(timezone.utc) + timedelta(days=7)

    # Store session in database
    session_id = uuid.uuid4()
    query = user_sessions.insert().values(
        id=session_id,
        user_id=user_id,
        session_token=session_hash,
        refresh_token=refresh_hash,
        expires_at=session_expires,
        refresh_expires_at=refresh_expires,
        ip_address=ip_address,
        user_agent=user_agent,
        is_active=True,
    )

    await db.execute(query)

    return session_token, refresh_token


async def get_session_by_token(session_token: str, db):
    """Get session by token"""
    session_hash = hash_token(session_token)
    query = (
        select(user_sessions, users)
        .select_from(
            user_sessions.join(users, user_sessions.c.user_id == users.c.id)
        )
        .where(
            and_(
                user_sessions.c.session_token == session_hash,
                user_sessions.c.is_active == True,
                user_sessions.c.expires_at > datetime.now(timezone.utc),
                users.c.is_active == True
            )
        )
    )
    result = await db.fetch_one(query)
    return result


async def update_session_access(session_id: uuid.UUID, db):
    """Update session last accessed time"""
    query = (
        update(user_sessions)
        .where(user_sessions.c.id == session_id)
        .values(last_accessed=datetime.now(timezone.utc))
    )
    await db.execute(query)


async def invalidate_session(session_token: str, db):
    """Invalidate a specific session"""
    session_hash = hash_token(session_token)
    query = (
        update(user_sessions)
        .where(user_sessions.c.session_token == session_hash)
        .values(is_active=False)
    )
    await db.execute(query)


async def invalidate_all_user_sessions(user_id: uuid.UUID, db, except_session_id: uuid.UUID = None):
    """Invalidate all sessions for a user (except optionally one)"""
    conditions = [user_sessions.c.user_id == user_id]
    if except_session_id:
        conditions.append(user_sessions.c.id != except_session_id)

    query = (
        update(user_sessions)
        .where(and_(*conditions))
        .values(is_active=False)
    )
    await db.execute(query)


async def get_user_sessions(user_id: uuid.UUID, db):
    """Get all active sessions for a user"""
    query = (
        select(user_sessions)
        .where(
            and_(
                user_sessions.c.user_id == user_id,
                user_sessions.c.is_active == True,
                user_sessions.c.expires_at > datetime.now(timezone.utc)
            )
        )
        .order_by(user_sessions.c.last_accessed.desc())
    )
    result = await db.fetch_all(query)
    return result


async def cleanup_expired_sessions(db):
    """Clean up expired sessions (should be run periodically)"""
    query = (
        update(user_sessions)
        .where(user_sessions.c.expires_at <= datetime.now(timezone.utc))
        .values(is_active=False)
    )
    await db.execute(query)


async def update_current_user(user_id: uuid.UUID, data: UserUpdateMe, db):
    """Update current user profile"""
    update_data = {}

    if data.email:
        # Check if email is already taken by another user
        existing_user = await get_user_by_email(data.email, db)
        if existing_user and existing_user.id != user_id:
            raise ValueError("Email is already taken")
        update_data["email"] = data.email.lower()
        update_data["is_verified"] = False  # Require re-verification for new email

    if data.first_name:
        update_data["first_name"] = data.first_name.strip()

    if data.last_name:
        update_data["last_name"] = data.last_name.strip()

    if update_data:
        update_data["updated_at"] = datetime.now(timezone.utc)

        query = (
            update(users)
            .where(users.c.id == user_id)
            .values(**update_data)
        )
        await db.execute(query)

        # If email was changed, create new verification token
        if "email" in update_data:
            await create_email_verification_token(user_id, db)

    return {"message": "User updated successfully"}


async def update_current_user_password(user_id: uuid.UUID, new_password_hash: str, db):
    """Update user password and invalidate all sessions except current"""
    query = (
        update(users)
        .where(users.c.id == user_id)
        .values(
            password=new_password_hash,
            password_changed_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
    )
    await db.execute(query)

    # Invalidate all other sessions for security
    await invalidate_all_user_sessions(user_id, db)

    return {"message": "Password updated successfully"}


# Password Reset Functions
async def create_password_reset_token(email: str, db) -> Optional[str]:
    """Create password reset token for user"""
    user = await get_user_by_email(email, db)
    if not user:
        return None

    # Invalidate any existing reset tokens
    await invalidate_password_reset_tokens(user.id, db)

    # Create new token
    token = generate_secure_token(32)
    token_hash = hash_token(token)
    expires_at = datetime.now(timezone.utc) + timedelta(hours=1)  # 1 hour expiry

    query = password_reset_tokens.insert().values(
        id=uuid.uuid4(),
        user_id=user.id,
        token=token_hash,
        expires_at=expires_at,
    )
    await db.execute(query)

    return token


async def verify_password_reset_token(token: str, db):
    """Verify password reset token and return user"""
    token_hash = hash_token(token)
    query = (
        select(password_reset_tokens, users)
        .select_from(
            password_reset_tokens.join(users, password_reset_tokens.c.user_id == users.c.id)
        )
        .where(
            and_(
                password_reset_tokens.c.token == token_hash,
                password_reset_tokens.c.used == False,
                password_reset_tokens.c.expires_at > datetime.now(timezone.utc),
                users.c.is_active == True
            )
        )
    )
    result = await db.fetch_one(query)
    return result


async def use_password_reset_token(token: str, new_password: str, db):
    """Use password reset token to change password"""
    token_data = await verify_password_reset_token(token, db)
    if not token_data:
        raise ValueError("Invalid or expired reset token")

    # Update password
    new_password_hash = get_password_hash(new_password)
    await update_current_user_password(token_data.user_id, new_password_hash, db)

    # Mark token as used
    token_hash = hash_token(token)
    query = (
        update(password_reset_tokens)
        .where(password_reset_tokens.c.token == token_hash)
        .values(used=True)
    )
    await db.execute(query)

    return {"message": "Password reset successfully"}


async def invalidate_password_reset_tokens(user_id: uuid.UUID, db):
    """Invalidate all password reset tokens for user"""
    query = (
        update(password_reset_tokens)
        .where(
            and_(
                password_reset_tokens.c.user_id == user_id,
                password_reset_tokens.c.used == False
            )
        )
        .values(used=True)
    )
    await db.execute(query)


# Email Verification Functions
async def create_email_verification_token(user_id: uuid.UUID, db) -> str:
    """Create email verification token"""
    # Invalidate existing tokens
    await invalidate_email_verification_tokens(user_id, db)

    token = generate_secure_token(32)
    token_hash = hash_token(token)
    expires_at = datetime.now(timezone.utc) + timedelta(hours=24)  # 24 hour expiry

    query = email_verification_tokens.insert().values(
        id=uuid.uuid4(),
        user_id=user_id,
        token=token_hash,
        expires_at=expires_at,
    )
    await db.execute(query)

    return token


async def verify_email_verification_token(token: str, db):
    """Verify email verification token"""
    token_hash = hash_token(token)
    query = (
        select(email_verification_tokens, users)
        .select_from(
            email_verification_tokens.join(users, email_verification_tokens.c.user_id == users.c.id)
        )
        .where(
            and_(
                email_verification_tokens.c.token == token_hash,
                email_verification_tokens.c.used == False,
                email_verification_tokens.c.expires_at > datetime.now(timezone.utc)
            )
        )
    )
    result = await db.fetch_one(query)
    return result


async def confirm_email_verification(token: str, db):
    """Confirm email verification"""
    token_data = await verify_email_verification_token(token, db)
    if not token_data:
        raise ValueError("Invalid or expired verification token")

    # Mark user as verified
    query = (
        update(users)
        .where(users.c.id == token_data.user_id)
        .values(
            is_verified=True,
            updated_at=datetime.now(timezone.utc)
        )
    )
    await db.execute(query)

    # Mark token as used
    token_hash = hash_token(token)
    query = (
        update(email_verification_tokens)
        .where(email_verification_tokens.c.token == token_hash)
        .values(used=True)
    )
    await db.execute(query)

    return {"message": "Email verified successfully"}


async def invalidate_email_verification_tokens(user_id: uuid.UUID, db):
    """Invalidate all email verification tokens for user"""
    query = (
        update(email_verification_tokens)
        .where(
            and_(
                email_verification_tokens.c.user_id == user_id,
                email_verification_tokens.c.used == False
            )
        )
        .values(used=True)
    )
    await db.execute(query)



