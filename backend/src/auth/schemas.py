from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator


class UserBase(BaseModel):
    email: EmailStr
    is_active: bool = True


class UserCreate(UserBase):
    password: str


class UserRegister(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)

    @validator('password')
    def validate_password(cls, v):
        from src.auth.security import validate_password_strength, is_password_compromised

        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")

        if is_password_compromised(v):
            raise ValueError("This password is commonly used and not secure. Please choose a different password.")

        return v


class UserRead(BaseModel):
    id: str
    email: EmailStr
    first_name: str
    last_name: str
    is_active: bool
    is_verified: bool
    last_login: Optional[datetime] = None
    created_at: datetime


class UserUpdateMe(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)


class UpdatePassword(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    @validator('new_password')
    def validate_new_password(cls, v):
        from src.auth.security import validate_password_strength, is_password_compromised

        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")

        if is_password_compromised(v):
            raise ValueError("This password is commonly used and not secure. Please choose a different password.")

        return v


# Session-based authentication responses
class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    remember_me: bool = False


class LoginResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserRead] = None
    redirect_url: str = "/dashboard"


class LogoutResponse(BaseModel):
    success: bool
    message: str
    redirect_url: str = "/"


# Password reset schemas
class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str

    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    @validator('new_password')
    def validate_new_password(cls, v):
        from src.auth.security import validate_password_strength, is_password_compromised

        is_valid, errors = validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {', '.join(errors)}")

        if is_password_compromised(v):
            raise ValueError("This password is commonly used and not secure. Please choose a different password.")

        return v


# Email verification schemas
class EmailVerificationRequest(BaseModel):
    email: EmailStr


class EmailVerificationConfirm(BaseModel):
    token: str


# Session management schemas
class SessionInfo(BaseModel):
    id: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    last_accessed: datetime
    is_current: bool = False


class UserSessions(BaseModel):
    sessions: list[SessionInfo]


# Security schemas
class SecuritySettings(BaseModel):
    two_factor_enabled: bool = False
    login_notifications: bool = True
    session_timeout: int = 30  # days


# Legacy JWT response (for API compatibility)
class JWTLoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    id: str
    exp: int
