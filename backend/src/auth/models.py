import uuid

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Table, Uuid, func, Text, Index

from src.database import metadata

users = Table(
    "users",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    <PERSON>umn("first_name", String(100), nullable=False),
    Column("last_name", String(100), nullable=False),
    <PERSON>umn("email", String(100), nullable=False, unique=True),
    <PERSON>umn("password", String, nullable=False),
    <PERSON>umn("is_active", Boolean, nullable=False, default=True),
    <PERSON>umn("is_verified", Boolean, nullable=False, default=False),
    <PERSON>umn("failed_login_attempts", Integer, nullable=False, default=0),
    <PERSON>umn("locked_until", DateTime, nullable=True),
    <PERSON>umn("last_login", DateTime, nullable=True),
    <PERSON>umn("password_changed_at", DateTime, nullable=True),
    <PERSON>umn("created_at", DateTime, server_default=func.now()),
    <PERSON>umn("updated_at", DateTime, onupdate=func.now()),
    Index("idx_users_email", "email"),
    Index("idx_users_active", "is_active"),
)

# Session management table for HTTP-only cookies
user_sessions = Table(
    "user_sessions",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    Column("user_id", Uuid, nullable=False),
    Column("session_token", String(255), nullable=False, unique=True),
    Column("refresh_token", String(255), nullable=True, unique=True),
    Column("expires_at", DateTime, nullable=False),
    Column("refresh_expires_at", DateTime, nullable=True),
    Column("ip_address", String(45), nullable=True),  # IPv6 support
    Column("user_agent", Text, nullable=True),
    Column("is_active", Boolean, nullable=False, default=True),
    Column("created_at", DateTime, server_default=func.now()),
    Column("last_accessed", DateTime, server_default=func.now()),
    Index("idx_sessions_token", "session_token"),
    Index("idx_sessions_user", "user_id"),
    Index("idx_sessions_active", "is_active"),
    Index("idx_sessions_expires", "expires_at"),
)

# Password reset tokens
password_reset_tokens = Table(
    "password_reset_tokens",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    Column("user_id", Uuid, nullable=False),
    Column("token", String(255), nullable=False, unique=True),
    Column("expires_at", DateTime, nullable=False),
    Column("used", Boolean, nullable=False, default=False),
    Column("created_at", DateTime, server_default=func.now()),
    Index("idx_reset_tokens_token", "token"),
    Index("idx_reset_tokens_user", "user_id"),
    Index("idx_reset_tokens_expires", "expires_at"),
)

# Email verification tokens
email_verification_tokens = Table(
    "email_verification_tokens",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    Column("user_id", Uuid, nullable=False),
    Column("token", String(255), nullable=False, unique=True),
    Column("expires_at", DateTime, nullable=False),
    Column("used", Boolean, nullable=False, default=False),
    Column("created_at", DateTime, server_default=func.now()),
    Index("idx_verification_tokens_token", "token"),
    Index("idx_verification_tokens_user", "user_id"),
    Index("idx_verification_tokens_expires", "expires_at"),
)
