import uuid

from sqlalchemy import <PERSON><PERSON>an, Column, DateTime, Integer, String, Table, Uuid, func

from src.database import metadata

users = Table(
    "users",
    metadata,
    Column("id", Uuid, primary_key=True, default=uuid.uuid4),
    <PERSON><PERSON><PERSON>("first_name", String(100), nullable=False),
    <PERSON><PERSON><PERSON>("last_name", String(100), nullable=False),
    <PERSON><PERSON><PERSON>("email", String(100), nullable=False),
    <PERSON><PERSON><PERSON>("password", String, nullable=False),
    <PERSON>umn("is_active", Boolean, nullable=False, default=False),
    <PERSON>umn("created_at", DateTime, server_default=func.now()),
    <PERSON>umn("updated_at", DateTime),
)
