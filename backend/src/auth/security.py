import secrets
import hashlib
import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Optional
from passlib.context import CryptContext
import jwt
from src.config import settings

# Enhanced password context with stronger settings
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12,  # Increased rounds for better security
)

# Session token length
SESSION_TOKEN_LENGTH = 64
REFRESH_TOKEN_LENGTH = 64

def get_password_hash(password: str) -> str:
    """Hash password with bcrypt"""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)


def generate_secure_token(length: int = SESSION_TOKEN_LENGTH) -> str:
    """Generate cryptographically secure random token"""
    return secrets.token_urlsafe(length)


def hash_token(token: str) -> str:
    """Hash token for secure storage"""
    return hashlib.sha256(token.encode()).hexdigest()


def verify_token_hash(token: str, token_hash: str) -> bool:
    """Verify token against its hash"""
    return hashlib.sha256(token.encode()).hexdigest() == token_hash


def generate_session_tokens() -> tuple[str, str, str, str]:
    """Generate session and refresh tokens with their hashes"""
    session_token = generate_secure_token(SESSION_TOKEN_LENGTH)
    refresh_token = generate_secure_token(REFRESH_TOKEN_LENGTH)
    session_hash = hash_token(session_token)
    refresh_hash = hash_token(refresh_token)
    return session_token, session_hash, refresh_token, refresh_hash


def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """Validate password strength and return errors if any"""
    errors = []

    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")

    if len(password) > 128:
        errors.append("Password must be less than 128 characters long")

    if not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")

    if not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")

    if not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one number")

    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        errors.append("Password must contain at least one special character")

    return len(errors) == 0, errors


def is_password_compromised(password: str) -> bool:
    """Check if password is in common password lists (simplified implementation)"""
    # In production, you might want to check against HaveIBeenPwned API
    # or maintain a local database of compromised passwords
    common_passwords = {
        "password", "123456", "password123", "admin", "qwerty",
        "letmein", "welcome", "monkey", "dragon", "password1"
    }
    return password.lower() in common_passwords


# Legacy JWT functions (kept for API compatibility if needed)
async def create_access_token(subject: str | Any, expires_delta: timedelta):
    """Create JWT access token (legacy - use sessions instead)"""
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode = {
        "sub": str(subject),
        "exp": expire,
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_jwt_token(token: str) -> Optional[dict]:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        return payload
    except jwt.PyJWTError:
        return None


def get_client_ip(request) -> str:
    """Extract client IP address from request"""
    # Check for forwarded headers (when behind proxy/load balancer)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fallback to direct connection
    return request.client.host if request.client else "unknown"


def get_user_agent(request) -> str:
    """Extract user agent from request"""
    return request.headers.get("User-Agent", "unknown")[:500]  # Limit length
