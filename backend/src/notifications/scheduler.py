# # src/scheduler.py
# from apscheduler.schedulers.asyncio import AsyncIOScheduler
#
# from src.notifications.notification_stream import NotificationBroadcaster
#
# # Create a single instance of NotificationBroadcaster that will be used by all scheduled jobs
# broadcaster = NotificationBroadcaster()
# scheduler = AsyncIOScheduler()
#
#
# async def schedule_reminders():
#     # scheduler.add_job(push_notification, "cron", hour=22, minute=0)  # 10PM
#     scheduler.add_job(push_notification, "cron", hour=00, minute=00)
#     scheduler.start()
#
#
# async def push_notification():
#     message = (
#         "Hey! Reminder to to write down atleast 3 task to accomplish  tomorrow  💪"
#     )
#     await broadcaster.push(message)
#     print("📢 Notification pushed")
#     print(message)

from zoneinfo import ZoneInfo

# src/scheduler.py
import httpx
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from loguru import logger

from src.config import settings
from src.notifications.notification_stream import NotificationBroadcaster

broadcaster = NotificationBroadcaster()
scheduler = AsyncIOScheduler(timezone=ZoneInfo("Africa/Mogadishu"))  # UTC+3


# ---------- 9 <USER> <GROUP> reminder ----------
async def remind_add_tasks():
    await broadcaster.push(
        "Hey! Reminder to write down at least 3 tasks you’ll tackle tomorrow 💪"
    )
    logger.info("📢 9 PM reminder pushed")


# ---------- midnight (00:00) agent run ----------
BACKEND_BASE_URL = settings.AGENT_URL


async def run_midnight_agent():
    async with httpx.AsyncClient(timeout=30) as client:
        r = await client.get(f"{BACKEND_BASE_URL}/api/v1/todoes")
        logger.info(f"🌙 Agent run at midnight → {r.status_code}")
        if r.status_code != 200:
            logger.error(f"⚠️ Agent endpoint failed: {r.text}")


# ---------- register jobs ----------
def start_scheduler() -> None:
    scheduler.add_job(remind_add_tasks, "cron", hour=21, minute=0, id="remind_21")
    # scheduler.add_job(run_midnight_agent, "cron", hour=0, minute=0, id="agent_00")
    scheduler.start()
    logger.info("✅ Production scheduler started")
