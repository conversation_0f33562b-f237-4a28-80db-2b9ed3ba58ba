import uuid

from sqlalchemy import (
    Column,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
    Time,
    Uuid,
    func,
)

from src.agent.schemas import TodoStatus
from src.database import metadata
from src.utils import TaskPriority

agent_task = Table(
    "agent_task",
    metadata,
    Column("id", Uuid(as_uuid=True), primary_key=True, default=uuid.uuid4),
    Column("task_name", String(100), nullable=False),
    Column("task_description", String, nullable=False),
    <PERSON>umn("task_priority", Enum(TaskPriority), nullable=False),
    <PERSON>umn("task_stimation", Time, nullable=False),
    <PERSON>umn(
        "tip",
        String,
    ),
    <PERSON>umn("created_at", DateTime, server_default=func.now()),
    <PERSON>umn("updated_at", DateTime, server_default=func.now(), onupdate=func.now()),
    <PERSON>umn("user_id", Uuid, Foreign<PERSON>ey("users.id"), nullable=False),
)


task_todo = Table(
    "task_todo",
    metadata,
    Column("id", Uuid(as_uuid=True), primary_key=True, default=uuid.uuid4),
    <PERSON>umn("task_num", Integer, nullable=False),
    Column("todo_name", String(100), nullable=False),
    Column("status", Enum(TodoStatus), nullable=False),
    Column("stimated_time", Time, nullable=False),
    Column("depends_on", String, nullable=True),
    Column("task_id", Uuid(as_uuid=True), ForeignKey("agent_task.id"), nullable=False),
)
