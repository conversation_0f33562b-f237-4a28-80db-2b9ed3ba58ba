# Complete End-to-End Prompt for Building SSE UI for TomoPlan FastAPI App

## Project Overview
Create a modern, responsive web UI for the existing TomoPlan FastAPI application using HTMX, TailwindCSS, Alpine.js, Chart.js, and FastHX library. The UI should provide real-time updates via Server-Sent Events (SSE) without modifying the existing backend API.

## Technology Stack Requirements
- **Backend Integration**: FastHX library for FastAPI template rendering
- **Frontend Framework**: HTMX for dynamic interactions
- **Styling**: TailwindCSS for responsive design
- **JavaScript**: Alpine.js for reactive components
- **Charts**: Chart.js for data visualization
- **Real-time**: Server-Sent Events (SSE) for live notifications
- **Templates**: Jinja2 for server-side rendering

## Existing API Structure
The FastAPI application has the following endpoints that need UI integration:

### Authentication Endpoints (`/api/v1/auth`)
- `POST /auth/sign up` - User registration
- `POST /auth/login` - User login (returns JWT token)
- `PATCH /auth/me` - Update user profile
- `PATCH /auth/me/password` - Update password

### Task Management Endpoints (`/api/v1/tasks`)
- `GET /tasks/tasks?limit=10&page=1` - Get paginated tasks
- `GET /tasks/task/{id}` - Get specific task
- `POST /tasks/addtask` - Create single task or multiple tasks
- `PUT /tasks/task/{id}` - Update task
- `DELETE /tasks/{id}` - Delete task

### AI Agent Endpoints (`/api/v1/agent`)
- `GET /agent/todoes` - Get AI-planned daily tasks
- `POST /agent/get-tasks` - Get user's tasks for planning

### Notifications Endpoint
- `GET /notifications/stream` - SSE endpoint for real-time notifications

### Data Models
**Task Priority Enum**: LOW, MEDIUM, HIGH, CRITICAL
**Task Schema**:
```json
{
  "id": "uuid",
  "name": "string (1-100 chars)",
  "description": "string (1-1000 chars)",
  "is_done": "boolean",
  "due_date": "datetime (optional)",
  "priority": "TaskPriority enum",
  "created_at": "date",
  "user_id": "uuid"
}
```

## UI Requirements

### 1. Authentication Pages
- **Login Page**: Clean form with email/password, JWT token handling
- **Registration Page**: Form with email, password, first_name, last_name
- **Profile Page**: Update user information and password
- **Authentication State**: Persistent login with localStorage JWT management

### 2. Dashboard Layout
- **Header**: Navigation with user menu, logout, notifications indicator
- **Sidebar**: Navigation menu (Dashboard, Tasks, AI Planning, Profile)
- **Main Content**: Dynamic content area
- **Footer**: App information and links

### 3. Task Management Interface
- **Task List View**:
  - Paginated table/cards with priority color coding
  - Filter by priority, completion status, due date
  - Search functionality
  - Bulk actions (mark complete, delete)
- **Task Creation Form**:
  - Modal or inline form
  - Priority selection with visual indicators
  - Due date picker
  - Rich text description
- **Task Detail View**:
  - Expandable cards or modal
  - Edit in place functionality
  - Status toggle
  - Delete confirmation
- **Task Analytics**:
  - Chart.js visualizations:
    - Tasks by priority (pie chart)
    - Completion trends (line chart)
    - Daily/weekly productivity (bar chart)

### 4. AI Planning Interface
- **Daily Planning View**:
  - Button to trigger AI planning
  - Display AI-generated task breakdown
  - Accept/modify AI suggestions
  - Visual task timeline
- **Planning Results**:
  - Structured display of AI recommendations
  - Option to save planned tasks
  - Roast messages when no tasks (with humor styling)

### 5. Real-time Notifications
- **SSE Integration**:
  - Connect to `/notifications/stream` endpoint
  - Toast notifications for real-time updates
  - Notification history panel
  - Visual indicators for unread notifications
- **Notification Types**:
  - Task reminders (9 PM daily)
  - AI planning results
  - Task completion celebrations
  - System updates

### 6. Responsive Design
- **Mobile-first**: TailwindCSS responsive utilities
- **Breakpoints**: sm, md, lg, xl layouts
- **Touch-friendly**: Appropriate button sizes and spacing
- **Progressive Enhancement**: Works without JavaScript

## Technical Implementation Requirements

### 1. FastHX Integration
```python
# Add to existing main.py
from fasthx import Jinja

# Configure FastHX with existing Jinja2Templates
jinja = Jinja(templates)

# Create new routes for UI rendering
@app.get("/dashboard")
async def dashboard(request: Request):
    return jinja.TemplateResponse("dashboard.html", {"request": request})
```

### 2. HTMX Implementation
- **hx-get/post/put/delete**: For API interactions
- **hx-target**: Update specific page sections
- **hx-swap**: Control content replacement strategy
- **hx-trigger**: Event-driven updates
- **hx-indicator**: Loading states
- **hx-confirm**: Delete confirmations

### 3. Alpine.js Components
- **Authentication state management**
- **Form validation and submission**
- **Modal controls**
- **Notification management**
- **Chart data reactivity**

### 4. Chart.js Integration
- **Task Priority Distribution**: Doughnut chart
- **Completion Trends**: Line chart with time series
- **Productivity Metrics**: Bar charts
- **Real-time Updates**: Chart data updates via HTMX

### 5. SSE Implementation
```javascript
// Alpine.js SSE component
Alpine.data('notifications', () => ({
    notifications: [],
    eventSource: null,
    init() {
        this.eventSource = new EventSource('/notifications/stream');
        this.eventSource.onmessage = (event) => {
            this.notifications.unshift(JSON.parse(event.data));
            this.showToast(event.data);
        };
    }
}));
```

## File Structure
```
frontend/
├── templates/
│   ├── base.html              # Base template with navigation
│   ├── auth/
│   │   ├── login.html         # Login form
│   │   ├── register.html      # Registration form
│   │   └── profile.html       # User profile
│   ├── dashboard/
│   │   ├── index.html         # Main dashboard
│   │   ├── tasks.html         # Task management
│   │   ├── planning.html      # AI planning interface
│   │   └── analytics.html     # Charts and metrics
│   └── components/
│       ├── task-card.html     # Reusable task component
│       ├── notification.html  # Notification toast
│       └── modals.html        # Modal components
├── static/
│   ├── css/
│   │   └── styles.css         # Custom CSS + TailwindCSS
│   ├── js/
│   │   ├── app.js            # Main Alpine.js app
│   │   ├── auth.js           # Authentication logic
│   │   ├── tasks.js          # Task management
│   │   ├── charts.js         # Chart.js configurations
│   │   └── notifications.js  # SSE handling
│   └── images/
│       └── icons/            # App icons and images
```

## Specific Implementation Tasks

### Phase 1: Setup and Authentication
1. Install and configure FastHX in existing FastAPI app
2. Create base template with TailwindCSS and Alpine.js
3. Implement login/register forms with HTMX
4. Set up JWT token management in localStorage
5. Create protected route middleware

### Phase 2: Task Management UI
1. Build task list with pagination and filtering
2. Create task creation/editing forms
3. Implement task status toggles and deletion
4. Add search and bulk operations
5. Style with priority color coding

### Phase 3: Real-time Features
1. Integrate SSE for notifications
2. Create notification toast system
3. Add real-time task updates
4. Implement notification history

### Phase 4: AI Planning Interface
1. Build AI planning trigger interface
2. Display AI recommendations
3. Handle task acceptance/modification
4. Show roast messages for empty states

### Phase 5: Analytics and Charts
1. Implement Chart.js visualizations
2. Create productivity metrics
3. Add real-time chart updates
4. Build analytics dashboard

### Phase 6: Polish and Optimization
1. Responsive design testing
2. Performance optimization
3. Accessibility improvements
4. Error handling and edge cases

## Security Considerations
- **JWT Token Security**: Secure storage and transmission
- **CSRF Protection**: HTMX CSRF token handling
- **Input Validation**: Client and server-side validation
- **XSS Prevention**: Proper template escaping
- **Rate Limiting**: Respect existing API rate limits

## Performance Requirements
- **Fast Initial Load**: Minimize JavaScript bundle size
- **Smooth Interactions**: Optimistic UI updates
- **Efficient SSE**: Proper connection management
- **Chart Performance**: Efficient data updates
- **Mobile Performance**: Optimized for mobile devices

## Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+
- **Progressive Enhancement**: Graceful degradation
- **Mobile Browsers**: iOS Safari, Chrome Mobile

## Testing Requirements
- **Unit Tests**: Alpine.js components
- **Integration Tests**: HTMX interactions
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Load testing with SSE
- **Accessibility Tests**: WCAG compliance

## Deployment Considerations
- **Static Assets**: Proper caching headers
- **CDN Integration**: TailwindCSS, Alpine.js, Chart.js
- **Environment Config**: Development vs production settings
- **SSL/HTTPS**: Required for SSE in production

## Success Criteria
1. **Functional**: All existing API endpoints have corresponding UI
2. **Real-time**: SSE notifications work seamlessly
3. **Responsive**: Works on all device sizes
4. **Performance**: Fast loading and smooth interactions
5. **Accessible**: WCAG 2.1 AA compliance
6. **Maintainable**: Clean, documented code structure

## Additional Features (Optional)
- **Dark Mode**: Toggle between light/dark themes
- **Keyboard Shortcuts**: Power user features
- **Offline Support**: Service worker for basic functionality
- **Export Features**: Task data export (CSV, JSON)
- **Drag & Drop**: Task reordering and priority changes
- **Calendar View**: Task due dates in calendar format

---

**Note**: This implementation should not modify any existing backend API endpoints. All integration should be done through the existing FastAPI routes and SSE endpoint. The FastHX library should be added as a new dependency to enhance the existing application with server-side rendering capabilities.
